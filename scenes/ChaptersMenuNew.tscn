[gd_scene load_steps=10 format=3 uid="uid://bxavoaqkqxqxq"]

[ext_resource type="Script" path="res://scripts/ChaptersMenuNew.gd" id="1_chapters"]
[ext_resource type="Texture2D" path="res://assets/Scalable screen/Scalable_1.png" id="2_bg"]
[ext_resource type="Theme" path="res://themes/GothicTheme.tres" id="3_theme"]
[ext_resource type="Texture2D" path="res://assets/Scalable screen/Scalable_3.png" id="4_card_bg"]
[ext_resource type="Texture2D" path="res://assets/Buttons/Buttons_1.png" id="5_button"]
[ext_resource type="Texture2D" path="res://assets/Buttons/Button_Selected.png" id="6_button_selected"]
[ext_resource type="Texture2D" path="res://assets/deviders/deviders2.png" id="7_divider"]
[ext_resource type="Texture2D" path="res://assets/Sliders/Arrow_1.png" id="8_arrow"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 28
font_color = Color(0.9, 0.8, 0.6, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.6)
shadow_offset = Vector2(2, 2)

[node name="ChaptersMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("3_theme")
script = ExtResource("1_chapters")

[node name="Background" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
patch_margin_left = 32
patch_margin_top = 32
patch_margin_right = 32
patch_margin_bottom = 32

[node name="SafeArea" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 40.0
offset_right = -20.0
offset_bottom = -20.0

[node name="MainContainer" type="VBoxContainer" parent="SafeArea"]
layout_mode = 2

[node name="Header" type="VBoxContainer" parent="SafeArea/MainContainer"]
layout_mode = 2

[node name="TitleContainer" type="CenterContainer" parent="SafeArea/MainContainer/Header"]
layout_mode = 2

[node name="ChapterTitle" type="Label" parent="SafeArea/MainContainer/Header/TitleContainer"]
layout_mode = 2
text = "KAPITOLA I"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="SafeArea/MainContainer/Header"]
layout_mode = 2
custom_minimum_size = Vector2(0, 15)

[node name="Divider1" type="TextureRect" parent="SafeArea/MainContainer/Header"]
layout_mode = 2
custom_minimum_size = Vector2(0, 8)
texture = ExtResource("7_divider")
stretch_mode = 1

[node name="Spacer2" type="Control" parent="SafeArea/MainContainer/Header"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ContentArea" type="Control" parent="SafeArea/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ChapterCard" type="NinePatchRect" parent="SafeArea/MainContainer/ContentArea"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -160.0
offset_top = -200.0
offset_right = 160.0
offset_bottom = 200.0
texture = ExtResource("4_card_bg")
patch_margin_left = 24
patch_margin_top = 24
patch_margin_right = 24
patch_margin_bottom = 24

[node name="CardContent" type="VBoxContainer" parent="SafeArea/MainContainer/ContentArea/ChapterCard"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="ChapterSubtitle" type="Label" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2
text = "BÚRLIVÁ CESTA"
horizontal_alignment = 1

[node name="Spacer3" type="Control" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2
custom_minimum_size = Vector2(0, 15)

[node name="PreviewContainer" type="CenterContainer" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2

[node name="ChapterPreview" type="TextureRect" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/PreviewContainer"]
layout_mode = 2
custom_minimum_size = Vector2(240, 240)
stretch_mode = 5

[node name="Spacer4" type="Control" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2
custom_minimum_size = Vector2(0, 15)

[node name="ChapterDescription" type="Label" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2
text = "Popis kapitoly"
horizontal_alignment = 1
autowrap_mode = 3
custom_minimum_size = Vector2(0, 60)

[node name="Spacer5" type="Control" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2
size_flags_vertical = 3

[node name="ProgressIndicator" type="HBoxContainer" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent"]
layout_mode = 2
alignment = 1

[node name="StatusLabel" type="Label" parent="SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ProgressIndicator"]
layout_mode = 2
text = "⚡ DOSTUPNÉ"
horizontal_alignment = 1

[node name="NavigationArea" type="VBoxContainer" parent="SafeArea/MainContainer"]
layout_mode = 2

[node name="Spacer6" type="Control" parent="SafeArea/MainContainer/NavigationArea"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="NavigationContainer" type="HBoxContainer" parent="SafeArea/MainContainer/NavigationArea"]
layout_mode = 2

[node name="PrevButton" type="TextureButton" parent="SafeArea/MainContainer/NavigationArea/NavigationContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("5_button")
texture_pressed = ExtResource("6_button_selected")
stretch_mode = 0

[node name="PrevLabel" type="Label" parent="SafeArea/MainContainer/NavigationArea/NavigationContainer/PrevButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "◀ PREDCH."
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer" type="Control" parent="SafeArea/MainContainer/NavigationArea/NavigationContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="NextButton" type="TextureButton" parent="SafeArea/MainContainer/NavigationArea/NavigationContainer"]
layout_mode = 2
custom_minimum_size = Vector2(120, 50)
texture_normal = ExtResource("5_button")
texture_pressed = ExtResource("6_button_selected")
stretch_mode = 0

[node name="NextLabel" type="Label" parent="SafeArea/MainContainer/NavigationArea/NavigationContainer/NextButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "ĎALŠIA ▶"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer7" type="Control" parent="SafeArea/MainContainer/NavigationArea"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="ActionContainer" type="VBoxContainer" parent="SafeArea/MainContainer/NavigationArea"]
layout_mode = 2

[node name="StartButton" type="TextureButton" parent="SafeArea/MainContainer/NavigationArea/ActionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 60)
texture_normal = ExtResource("6_button_selected")
stretch_mode = 0

[node name="StartLabel" type="Label" parent="SafeArea/MainContainer/NavigationArea/ActionContainer/StartButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "SPUSTIŤ KAPITOLU"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer8" type="Control" parent="SafeArea/MainContainer/NavigationArea/ActionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 15)

[node name="BackButton" type="TextureButton" parent="SafeArea/MainContainer/NavigationArea/ActionContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
texture_normal = ExtResource("5_button")
texture_pressed = ExtResource("6_button_selected")
stretch_mode = 0

[node name="BackLabel" type="Label" parent="SafeArea/MainContainer/NavigationArea/ActionContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "← SPÄŤ"
horizontal_alignment = 1
vertical_alignment = 1
