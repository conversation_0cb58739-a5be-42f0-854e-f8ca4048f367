[gd_scene load_steps=5 format=3 uid="uid://cyavoaqkqxqxq"]

[ext_resource type="Script" uid="uid://cjfel0t8yu0w7" path="res://scripts/ChaptersMenu.gd" id="1_chapters"]
[ext_resource type="Texture2D" uid="uid://cy11cfsv7he1f" path="res://assets/MENU.png" id="2_bg"]
[ext_resource type="Theme" uid="uid://2g01d2kieu86" path="res://themes/GothicTheme.tres" id="3_theme"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 32
font_color = Color(0.831, 0.686, 0.216, 1)
outline_size = 3
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 2
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(3, 3)

[node name="ChaptersMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("3_theme")
script = ExtResource("1_chapters")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_bg")
stretch_mode = 1

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="ChapterTitle" type="Label" parent="MainContainer"]
layout_mode = 2
text = "KAPITOLA I"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ChapterSubtitle" type="Label" parent="MainContainer"]
layout_mode = 2
text = "BÚRLIVÁ CESTA"
horizontal_alignment = 1

[node name="Spacer2" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ChapterPreviewContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ChapterPreview" type="TextureRect" parent="MainContainer/ChapterPreviewContainer"]
custom_minimum_size = Vector2(280, 280)
layout_mode = 2
size_flags_horizontal = 4
stretch_mode = 5

[node name="Spacer3" type="Control" parent="MainContainer/ChapterPreviewContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="ChapterDescription" type="Label" parent="MainContainer/ChapterPreviewContainer"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2
text = "Popis kapitoly"
horizontal_alignment = 1
autowrap_mode = 3

[node name="NavigationContainer" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2

[node name="PrevButton" type="Button" parent="MainContainer/NavigationContainer"]
custom_minimum_size = Vector2(140, 50)
layout_mode = 2
text = "◀ PREDCH."

[node name="Spacer" type="Control" parent="MainContainer/NavigationContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="NextButton" type="Button" parent="MainContainer/NavigationContainer"]
custom_minimum_size = Vector2(140, 50)
layout_mode = 2
text = "ĎALŠIA ▶"

[node name="Spacer5" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="StartButton" type="Button" parent="MainContainer"]
custom_minimum_size = Vector2(0, 60)
layout_mode = 2
text = "SPUSTIŤ KAPITOLU"

[node name="Spacer6" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="BackButton" type="Button" parent="MainContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
text = "SPÄŤ"
