[gd_scene load_steps=16 format=3 uid="uid://cbuf1q3xsse3q"]

[ext_resource type="Script" uid="uid://cipjcc7bkh1pc" path="res://addons/dialogue_manager/views/main_view.gd" id="1_h6qfq"]
[ext_resource type="PackedScene" uid="uid://civ6shmka5e8u" path="res://addons/dialogue_manager/components/code_edit.tscn" id="2_f73fm"]
[ext_resource type="PackedScene" uid="uid://dnufpcdrreva3" path="res://addons/dialogue_manager/components/files_list.tscn" id="2_npj2k"]
[ext_resource type="PackedScene" uid="uid://ctns6ouwwd68i" path="res://addons/dialogue_manager/components/title_list.tscn" id="2_onb4i"]
[ext_resource type="PackedScene" uid="uid://co8yl23idiwbi" path="res://addons/dialogue_manager/components/update_button.tscn" id="2_ph3vs"]
[ext_resource type="PackedScene" uid="uid://gr8nakpbrhby" path="res://addons/dialogue_manager/components/search_and_replace.tscn" id="6_ylh0t"]
[ext_resource type="PackedScene" uid="uid://cs8pwrxr5vxix" path="res://addons/dialogue_manager/components/errors_panel.tscn" id="7_5cvl4"]
[ext_resource type="Script" uid="uid://klpiq4tk3t7a" path="res://addons/dialogue_manager/components/code_edit_syntax_highlighter.gd" id="7_necsa"]
[ext_resource type="Texture2D" uid="uid://cnm67htuohhlo" path="res://addons/dialogue_manager/assets/banner.png" id="9_y6rqu"]
[ext_resource type="PackedScene" uid="uid://0n7hwviyyly4" path="res://addons/dialogue_manager/components/find_in_files.tscn" id="10_yold3"]

[sub_resource type="Image" id="Image_y6rqu"]
data = {
"data": PackedByteArray(255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 93, 93, 255, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 231, 255, 94, 94, 54, 255, 94, 94, 57, 255, 93, 93, 233, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 231, 255, 94, 94, 54, 255, 94, 94, 57, 255, 93, 93, 233, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 97, 97, 42, 255, 255, 255, 0, 255, 255, 255, 0, 255, 97, 97, 42, 255, 93, 93, 233, 255, 93, 93, 232, 255, 93, 93, 41, 255, 255, 255, 0, 255, 255, 255, 0, 255, 97, 97, 42, 255, 93, 93, 233, 255, 93, 93, 232, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 44, 255, 255, 255, 0, 255, 97, 97, 42, 255, 97, 97, 42, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 44, 255, 255, 255, 0, 255, 97, 97, 42, 255, 97, 97, 42, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 235, 255, 94, 94, 234, 255, 95, 95, 43, 255, 255, 255, 0, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 235, 255, 94, 94, 234, 255, 95, 95, 43, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 235, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 233, 255, 95, 95, 59, 255, 96, 96, 61, 255, 93, 93, 235, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 233, 255, 95, 95, 59, 255, 96, 96, 61, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0),
"format": "RGBA8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id="ImageTexture_ka3gk"]
image = SubResource("Image_y6rqu")

[sub_resource type="Image" id="Image_mpdoc"]
data = {
"data": PackedByteArray(255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 93, 93, 255, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 94, 94, 127, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 231, 255, 94, 94, 54, 255, 94, 94, 57, 255, 93, 93, 233, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 231, 255, 94, 94, 54, 255, 94, 94, 57, 255, 93, 93, 233, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 97, 97, 42, 255, 255, 255, 0, 255, 255, 255, 0, 255, 97, 97, 42, 255, 93, 93, 233, 255, 93, 93, 232, 255, 93, 93, 41, 255, 255, 255, 0, 255, 255, 255, 0, 255, 97, 97, 42, 255, 93, 93, 233, 255, 93, 93, 232, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 44, 255, 255, 255, 0, 255, 97, 97, 42, 255, 97, 97, 42, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 44, 255, 255, 255, 0, 255, 97, 97, 42, 255, 97, 97, 42, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 235, 255, 94, 94, 234, 255, 95, 95, 43, 255, 255, 255, 0, 255, 255, 255, 0, 255, 96, 96, 45, 255, 93, 93, 235, 255, 94, 94, 234, 255, 95, 95, 43, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 235, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 233, 255, 95, 95, 59, 255, 96, 96, 61, 255, 93, 93, 235, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 233, 255, 95, 95, 59, 255, 96, 96, 61, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 93, 93, 255, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0, 255, 255, 255, 0),
"format": "RGBA8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id="ImageTexture_57eek"]
image = SubResource("Image_mpdoc")

[sub_resource type="SyntaxHighlighter" id="SyntaxHighlighter_xv2j4"]
script = ExtResource("7_necsa")

[node name="MainView" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
script = ExtResource("1_h6qfq")

[node name="ParseTimer" type="Timer" parent="."]

[node name="Margin" type="MarginContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_vertical = 3
theme_override_constants/margin_left = 5
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 5
metadata/_edit_layout_mode = 1

[node name="Content" type="HSplitContainer" parent="Margin"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
dragger_visibility = 1

[node name="SidePanel" type="VBoxContainer" parent="Margin/Content"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
size_flags_horizontal = 3

[node name="Toolbar" type="HBoxContainer" parent="Margin/Content/SidePanel"]
layout_mode = 2

[node name="NewButton" type="Button" parent="Margin/Content/SidePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Start a new file"
flat = true

[node name="OpenButton" type="MenuButton" parent="Margin/Content/SidePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Open a file"
item_count = 9
popup/item_0/text = "Open..."
popup/item_0/icon = SubResource("ImageTexture_ka3gk")
popup/item_0/id = 100
popup/item_1/icon = SubResource("ImageTexture_ka3gk")
popup/item_1/id = 101
popup/item_2/id = -1
popup/item_2/separator = true
popup/item_3/text = "res://examples/dialogue.dialogue"
popup/item_3/icon = SubResource("ImageTexture_ka3gk")
popup/item_3/id = 3
popup/item_4/text = "res://examples/dialogue_with_input.dialogue"
popup/item_4/icon = SubResource("ImageTexture_ka3gk")
popup/item_4/id = 4
popup/item_5/text = "res://examples/dialogue_for_point_n_click.dialogue"
popup/item_5/icon = SubResource("ImageTexture_ka3gk")
popup/item_5/id = 5
popup/item_6/text = "res://examples/dialogue_for_visual_novel.dialogue"
popup/item_6/icon = SubResource("ImageTexture_ka3gk")
popup/item_6/id = 6
popup/item_7/id = -1
popup/item_7/separator = true
popup/item_8/text = "Clear recent files"
popup/item_8/id = 102

[node name="SaveAllButton" type="Button" parent="Margin/Content/SidePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
disabled = true
flat = true

[node name="FindInFilesButton" type="Button" parent="Margin/Content/SidePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Find in files..."
flat = true

[node name="Bookmarks" type="VSplitContainer" parent="Margin/Content/SidePanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="FilesList" parent="Margin/Content/SidePanel/Bookmarks" instance=ExtResource("2_npj2k")]
unique_name_in_owner = true
visible = false
layout_mode = 2

[node name="FilesPopupMenu" type="PopupMenu" parent="Margin/Content/SidePanel/Bookmarks/FilesList"]
unique_name_in_owner = true

[node name="TitleList" parent="Margin/Content/SidePanel/Bookmarks" instance=ExtResource("2_onb4i")]
unique_name_in_owner = true
visible = false
layout_mode = 2

[node name="CodePanel" type="VBoxContainer" parent="Margin/Content"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 4.0

[node name="Toolbar" type="HBoxContainer" parent="Margin/Content/CodePanel"]
layout_mode = 2

[node name="InsertButton" type="MenuButton" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
disabled = true
text = "Insert"
item_count = 15
popup/item_0/text = "Wave BBCode"
popup/item_0/icon = SubResource("ImageTexture_57eek")
popup/item_0/id = 0
popup/item_1/text = "Shake BBCode"
popup/item_1/icon = SubResource("ImageTexture_57eek")
popup/item_1/id = 1
popup/item_2/id = -1
popup/item_2/separator = true
popup/item_3/text = "Typing pause"
popup/item_3/icon = SubResource("ImageTexture_57eek")
popup/item_3/id = 3
popup/item_4/text = "Typing speed change"
popup/item_4/icon = SubResource("ImageTexture_57eek")
popup/item_4/id = 4
popup/item_5/text = "Auto advance"
popup/item_5/icon = SubResource("ImageTexture_57eek")
popup/item_5/id = 5
popup/item_6/text = "Templates"
popup/item_6/id = -1
popup/item_6/separator = true
popup/item_7/text = "Title"
popup/item_7/icon = SubResource("ImageTexture_57eek")
popup/item_7/id = 6
popup/item_8/text = "Dialogue"
popup/item_8/icon = SubResource("ImageTexture_57eek")
popup/item_8/id = 7
popup/item_9/text = "Response"
popup/item_9/icon = SubResource("ImageTexture_57eek")
popup/item_9/id = 8
popup/item_10/text = "Random lines"
popup/item_10/icon = SubResource("ImageTexture_57eek")
popup/item_10/id = 9
popup/item_11/text = "Random text"
popup/item_11/icon = SubResource("ImageTexture_57eek")
popup/item_11/id = 10
popup/item_12/text = "Actions"
popup/item_12/id = -1
popup/item_12/separator = true
popup/item_13/text = "Jump to title"
popup/item_13/icon = SubResource("ImageTexture_57eek")
popup/item_13/id = 11
popup/item_14/text = "End dialogue"
popup/item_14/icon = SubResource("ImageTexture_57eek")
popup/item_14/id = 12

[node name="TranslationsButton" type="MenuButton" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
disabled = true
text = "Translations"
item_count = 5
popup/item_0/text = "Generate line IDs"
popup/item_0/icon = SubResource("ImageTexture_57eek")
popup/item_0/id = 100
popup/item_1/id = -1
popup/item_1/separator = true
popup/item_2/text = "Save character names to CSV..."
popup/item_2/icon = SubResource("ImageTexture_57eek")
popup/item_2/id = 201
popup/item_3/text = "Save lines to CSV..."
popup/item_3/icon = SubResource("ImageTexture_57eek")
popup/item_3/id = 202
popup/item_4/text = "Import line changes from CSV..."
popup/item_4/icon = SubResource("ImageTexture_57eek")
popup/item_4/id = 203

[node name="Separator" type="VSeparator" parent="Margin/Content/CodePanel/Toolbar"]
layout_mode = 2

[node name="SearchButton" type="Button" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Search for text"
disabled = true
toggle_mode = true
flat = true

[node name="Separator2" type="VSeparator" parent="Margin/Content/CodePanel/Toolbar"]
layout_mode = 2

[node name="TestButton" type="Button" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Test dialogue"
disabled = true
flat = true

[node name="TestLineButton" type="Button" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Test dialogue"
disabled = true
flat = true

[node name="Spacer2" type="Control" parent="Margin/Content/CodePanel/Toolbar"]
layout_mode = 2
size_flags_horizontal = 3

[node name="SupportButton" type="Button" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
tooltip_text = "Support Dialogue Manager"
text = "Sponsor"
flat = true

[node name="Separator4" type="VSeparator" parent="Margin/Content/CodePanel/Toolbar"]
layout_mode = 2

[node name="DocsButton" type="Button" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
layout_mode = 2
text = "Docs"
flat = true

[node name="VersionLabel" type="Label" parent="Margin/Content/CodePanel/Toolbar"]
unique_name_in_owner = true
modulate = Color(1, 1, 1, 0.490196)
layout_mode = 2
text = "v2.42.2"
vertical_alignment = 1

[node name="UpdateButton" parent="Margin/Content/CodePanel/Toolbar" instance=ExtResource("2_ph3vs")]
unique_name_in_owner = true
layout_mode = 2
text = "v2.44.1 available"

[node name="SearchAndReplace" parent="Margin/Content/CodePanel" instance=ExtResource("6_ylh0t")]
unique_name_in_owner = true
layout_mode = 2

[node name="CodeEdit" parent="Margin/Content/CodePanel" instance=ExtResource("2_f73fm")]
unique_name_in_owner = true
visible = false
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_colors/font_color = Color(0.972549, 0.972549, 0.94902, 1)
theme_override_colors/background_color = Color(0.156863, 0.164706, 0.211765, 1)
theme_override_colors/current_line_color = Color(0.266667, 0.278431, 0.352941, 0.243137)
theme_override_font_sizes/font_size = 14
theme_override_colors/bookmark_color = Color(1, 0.333333, 0.333333, 1)
text = "~ start

Nathan: Hi, I'm Nathan and this is Coco.
Coco: Meow.
Nathan: Here are some response options.
- First one
	Nathan: You picked the first one.
- Second one
	Nathan: You picked the second one.
- Start again => start
- End the conversation => END
Nathan: I hope this example is helpful.
Coco: Meow.

=> END"
scroll_smooth = true
syntax_highlighter = SubResource("SyntaxHighlighter_xv2j4")

[node name="ErrorsPanel" parent="Margin/Content/CodePanel" instance=ExtResource("7_5cvl4")]
unique_name_in_owner = true
layout_mode = 2

[node name="Banner" type="CenterContainer" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 34.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="PanelContainer" type="VBoxContainer" parent="Banner"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="BannerImage" type="TextureRect" parent="Banner/PanelContainer"]
custom_minimum_size = Vector2(600, 200)
layout_mode = 2
mouse_filter = 0
mouse_default_cursor_shape = 2
texture = ExtResource("9_y6rqu")
expand_mode = 3

[node name="HBoxContainer" type="HBoxContainer" parent="Banner/PanelContainer"]
layout_mode = 2
theme_override_constants/separation = 5

[node name="BannerNewButton" type="Button" parent="Banner/PanelContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "New Dialogue..."

[node name="BannerQuickOpen" type="Button" parent="Banner/PanelContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "Quick open..."

[node name="BannerExamples" type="Button" parent="Banner/PanelContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "Example projects..."

[node name="NewDialog" type="FileDialog" parent="."]
size = Vector2i(900, 750)
min_size = Vector2i(600, 500)
dialog_hide_on_ok = true
filters = PackedStringArray("*.dialogue ; Dialogue")

[node name="SaveDialog" type="FileDialog" parent="."]
size = Vector2i(900, 750)
min_size = Vector2i(600, 500)
dialog_hide_on_ok = true
filters = PackedStringArray("*.dialogue ; Dialogue")

[node name="OpenDialog" type="FileDialog" parent="."]
title = "Open a File"
size = Vector2i(900, 750)
min_size = Vector2i(600, 500)
ok_button_text = "Open"
dialog_hide_on_ok = true
file_mode = 0
filters = PackedStringArray("*.dialogue ; Dialogue")

[node name="QuickOpenDialog" type="ConfirmationDialog" parent="."]
title = "Quick open"
size = Vector2i(600, 900)
min_size = Vector2i(400, 600)
ok_button_text = "Open"

[node name="QuickOpenFilesList" parent="QuickOpenDialog" instance=ExtResource("2_npj2k")]

[node name="ExportDialog" type="FileDialog" parent="."]
size = Vector2i(900, 750)
min_size = Vector2i(600, 500)

[node name="ImportDialog" type="FileDialog" parent="."]
title = "Open a File"
size = Vector2i(900, 750)
min_size = Vector2i(600, 500)
ok_button_text = "Open"
file_mode = 0
filters = PackedStringArray("*.csv ; Translation CSV")

[node name="ErrorsDialog" type="AcceptDialog" parent="."]
title = "Error"
dialog_text = "You have errors in your script. Fix them and then try again."

[node name="BuildErrorDialog" type="AcceptDialog" parent="."]
title = "Errors"
dialog_text = "You need to fix dialogue errors before you can run your game."

[node name="CloseConfirmationDialog" type="ConfirmationDialog" parent="."]
title = "Unsaved changes"
ok_button_text = "Save changes"

[node name="UpdatedDialog" type="AcceptDialog" parent="."]
title = "Updated"
size = Vector2i(191, 100)
dialog_text = "You're now up to date!"

[node name="FindInFilesDialog" type="AcceptDialog" parent="."]
title = "Find in files"
size = Vector2i(1200, 900)
min_size = Vector2i(800, 600)
ok_button_text = "Done"

[node name="FindInFiles" parent="FindInFilesDialog" node_paths=PackedStringArray("main_view", "code_edit") instance=ExtResource("10_yold3")]
custom_minimum_size = Vector2(400, 400)
offset_left = 8.0
offset_top = 8.0
offset_right = -8.0
offset_bottom = -49.0
main_view = NodePath("../..")
code_edit = NodePath("../../Margin/Content/CodePanel/CodeEdit")

[connection signal="theme_changed" from="." to="." method="_on_main_view_theme_changed"]
[connection signal="visibility_changed" from="." to="." method="_on_main_view_visibility_changed"]
[connection signal="timeout" from="ParseTimer" to="." method="_on_parse_timer_timeout"]
[connection signal="pressed" from="Margin/Content/SidePanel/Toolbar/NewButton" to="." method="_on_new_button_pressed"]
[connection signal="about_to_popup" from="Margin/Content/SidePanel/Toolbar/OpenButton" to="." method="_on_open_button_about_to_popup"]
[connection signal="pressed" from="Margin/Content/SidePanel/Toolbar/SaveAllButton" to="." method="_on_save_all_button_pressed"]
[connection signal="pressed" from="Margin/Content/SidePanel/Toolbar/FindInFilesButton" to="." method="_on_find_in_files_button_pressed"]
[connection signal="file_middle_clicked" from="Margin/Content/SidePanel/Bookmarks/FilesList" to="." method="_on_files_list_file_middle_clicked"]
[connection signal="file_popup_menu_requested" from="Margin/Content/SidePanel/Bookmarks/FilesList" to="." method="_on_files_list_file_popup_menu_requested"]
[connection signal="file_selected" from="Margin/Content/SidePanel/Bookmarks/FilesList" to="." method="_on_files_list_file_selected"]
[connection signal="about_to_popup" from="Margin/Content/SidePanel/Bookmarks/FilesList/FilesPopupMenu" to="." method="_on_files_popup_menu_about_to_popup"]
[connection signal="id_pressed" from="Margin/Content/SidePanel/Bookmarks/FilesList/FilesPopupMenu" to="." method="_on_files_popup_menu_id_pressed"]
[connection signal="title_selected" from="Margin/Content/SidePanel/Bookmarks/TitleList" to="." method="_on_title_list_title_selected"]
[connection signal="toggled" from="Margin/Content/CodePanel/Toolbar/SearchButton" to="." method="_on_search_button_toggled"]
[connection signal="pressed" from="Margin/Content/CodePanel/Toolbar/TestButton" to="." method="_on_test_button_pressed"]
[connection signal="pressed" from="Margin/Content/CodePanel/Toolbar/TestLineButton" to="." method="_on_test_line_button_pressed"]
[connection signal="pressed" from="Margin/Content/CodePanel/Toolbar/SupportButton" to="." method="_on_support_button_pressed"]
[connection signal="pressed" from="Margin/Content/CodePanel/Toolbar/DocsButton" to="." method="_on_docs_button_pressed"]
[connection signal="close_requested" from="Margin/Content/CodePanel/SearchAndReplace" to="." method="_on_search_and_replace_close_requested"]
[connection signal="open_requested" from="Margin/Content/CodePanel/SearchAndReplace" to="." method="_on_search_and_replace_open_requested"]
[connection signal="active_title_change" from="Margin/Content/CodePanel/CodeEdit" to="." method="_on_code_edit_active_title_change"]
[connection signal="caret_changed" from="Margin/Content/CodePanel/CodeEdit" to="." method="_on_code_edit_caret_changed"]
[connection signal="error_clicked" from="Margin/Content/CodePanel/CodeEdit" to="." method="_on_code_edit_error_clicked"]
[connection signal="external_file_requested" from="Margin/Content/CodePanel/CodeEdit" to="." method="_on_code_edit_external_file_requested"]
[connection signal="text_changed" from="Margin/Content/CodePanel/CodeEdit" to="." method="_on_code_edit_text_changed"]
[connection signal="error_pressed" from="Margin/Content/CodePanel/ErrorsPanel" to="." method="_on_errors_panel_error_pressed"]
[connection signal="gui_input" from="Banner/PanelContainer/BannerImage" to="." method="_on_banner_image_gui_input"]
[connection signal="pressed" from="Banner/PanelContainer/HBoxContainer/BannerNewButton" to="." method="_on_banner_new_button_pressed"]
[connection signal="pressed" from="Banner/PanelContainer/HBoxContainer/BannerQuickOpen" to="." method="_on_banner_quick_open_pressed"]
[connection signal="pressed" from="Banner/PanelContainer/HBoxContainer/BannerExamples" to="." method="_on_banner_examples_pressed"]
[connection signal="confirmed" from="NewDialog" to="." method="_on_new_dialog_confirmed"]
[connection signal="file_selected" from="NewDialog" to="." method="_on_new_dialog_file_selected"]
[connection signal="file_selected" from="SaveDialog" to="." method="_on_save_dialog_file_selected"]
[connection signal="file_selected" from="OpenDialog" to="." method="_on_open_dialog_file_selected"]
[connection signal="confirmed" from="QuickOpenDialog" to="." method="_on_quick_open_dialog_confirmed"]
[connection signal="file_double_clicked" from="QuickOpenDialog/QuickOpenFilesList" to="." method="_on_quick_open_files_list_file_double_clicked"]
[connection signal="file_selected" from="ExportDialog" to="." method="_on_export_dialog_file_selected"]
[connection signal="file_selected" from="ImportDialog" to="." method="_on_import_dialog_file_selected"]
[connection signal="confirmed" from="CloseConfirmationDialog" to="." method="_on_close_confirmation_dialog_confirmed"]
[connection signal="custom_action" from="CloseConfirmationDialog" to="." method="_on_close_confirmation_dialog_custom_action"]
[connection signal="result_selected" from="FindInFilesDialog/FindInFiles" to="." method="_on_find_in_files_result_selected"]
