msgid ""
msgstr ""
"Project-Id-Version: Dialogue Manager\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.2.2\n"

msgid "start_a_new_file"
msgstr "Start a new file"

msgid "open_a_file"
msgstr "Open a file"

msgid "open.open"
msgstr "Open..."

msgid "open.quick_open"
msgstr "Quick open..."

msgid "open.no_recent_files"
msgstr "No recent files"

msgid "open.clear_recent_files"
msgstr "Clear recent files"

msgid "save_all_files"
msgstr "Save all files"

msgid "all"
msgstr "All"

msgid "find_in_files"
msgstr "Find in files..."

msgid "test_dialogue"
msgstr "Test dialogue from start of file"

msgid "test_dialogue_from_line"
msgstr "Test dialogue from current line"

msgid "search_for_text"
msgstr "Search for text"

msgid "insert"
msgstr "Insert"

msgid "translations"
msgstr "Translations"

msgid "sponsor"
msgstr "Sponsor"

msgid "show_support"
msgstr "Support Dialogue Manager"

msgid "docs"
msgstr "Docs"

msgid "insert.wave_bbcode"
msgstr "Wave BBCode"

msgid "insert.shake_bbcode"
msgstr "Shake BBCode"

msgid "insert.typing_pause"
msgstr "Typing pause"

msgid "insert.typing_speed_change"
msgstr "Typing speed change"

msgid "insert.auto_advance"
msgstr "Auto advance"

msgid "insert.templates"
msgstr "Templates"

msgid "insert.title"
msgstr "Title"

msgid "insert.dialogue"
msgstr "Dialogue"

msgid "insert.response"
msgstr "Response"

msgid "insert.random_lines"
msgstr "Random lines"

msgid "insert.random_text"
msgstr "Random text"

msgid "insert.actions"
msgstr "Actions"

msgid "insert.jump"
msgstr "Jump to title"

msgid "insert.end_dialogue"
msgstr "End dialogue"

msgid "generate_line_ids"
msgstr "Generate line IDs"

msgid "use_uuid_only_for_ids"
msgstr "Use UUID only for IDs"

msgid "set_id_prefix_length"
msgstr "Set ID prefix length"

msgid "id_prefix_length"
msgstr "ID prefix length:"

msgid "save_characters_to_csv"
msgstr "Save character names to CSV..."

msgid "save_to_csv"
msgstr "Save lines to CSV..."

msgid "import_from_csv"
msgstr "Import line changes from CSV..."

msgid "confirm_close"
msgstr "Save changes to '{path}'?"

msgid "confirm_close.save"
msgstr "Save changes"

msgid "confirm_close.discard"
msgstr "Discard"

msgid "buffer.save"
msgstr "Save"

msgid "buffer.save_as"
msgstr "Save as..."

msgid "buffer.close"
msgstr "Close"

msgid "buffer.close_all"
msgstr "Close all"

msgid "buffer.close_other_files"
msgstr "Close other files"

msgid "buffer.copy_file_path"
msgstr "Copy file path"

msgid "buffer.show_in_filesystem"
msgstr "Show in FileSystem"

msgid "n_of_n"
msgstr "{index} of {total}"

msgid "search.find"
msgstr "Find:"

msgid "search.find_all"
msgstr "Find all..."

msgid "search.placeholder"
msgstr "Text to search for"

msgid "search.replace_placeholder"
msgstr "Text to replace it with"

msgid "search.replace_selected"
msgstr "Replace selected"

msgid "search.previous"
msgstr "Previous"

msgid "search.next"
msgstr "Next"

msgid "search.match_case"
msgstr "Match case"

msgid "search.toggle_replace"
msgstr "Replace"

msgid "search.replace_with"
msgstr "Replace with:"

msgid "search.replace"
msgstr "Replace"

msgid "search.replace_all"
msgstr "Replace all"

msgid "files_list.filter"
msgstr "Filter files"

msgid "titles_list.filter"
msgstr "Filter titles"

msgid "errors.key_not_found"
msgstr "Key \"{key}\" not found."

msgid "errors.line_and_message"
msgstr "Error at {line}, {column}: {message}"

msgid "errors_in_script"
msgstr "You have errors in your script. Fix them and then try again."

msgid "errors_with_build"
msgstr "You need to fix dialogue errors before you can run your game."

msgid "errors.import_errors"
msgstr "There are errors in this imported file."

msgid "errors.already_imported"
msgstr "File already imported."

msgid "errors.duplicate_import"
msgstr "Duplicate import name."

msgid "errors.unknown_using"
msgstr "Unknown autoload in using statement."

msgid "errors.empty_title"
msgstr "Titles cannot be empty."

msgid "errors.duplicate_title"
msgstr "There is already a title with that name."

msgid "errors.invalid_title_string"
msgstr "Titles can only contain alphanumeric characters and numbers."

msgid "errors.invalid_title_number"
msgstr "Titles cannot begin with a number."

msgid "errors.unknown_title"
msgstr "Unknown title."

msgid "errors.jump_to_invalid_title"
msgstr "This jump is pointing to an invalid title."

msgid "errors.title_has_no_content"
msgstr "That title has no content. Maybe change this to a \"=> END\"."

msgid "errors.invalid_expression"
msgstr "Expression is invalid."

msgid "errors.unexpected_condition"
msgstr "Unexpected condition."

msgid "errors.duplicate_id"
msgstr "This ID is already on another line."

msgid "errors.missing_id"
msgstr "This line is missing an ID."

msgid "errors.invalid_indentation"
msgstr "Invalid indentation."

msgid "errors.condition_has_no_content"
msgstr "A condition line needs an indented line below it."

msgid "errors.incomplete_expression"
msgstr "Incomplete expression."

msgid "errors.invalid_expression_for_value"
msgstr "Invalid expression for value."

msgid "errors.file_not_found"
msgstr "File not found."

msgid "errors.unexpected_end_of_expression"
msgstr "Unexpected end of expression."

msgid "errors.unexpected_function"
msgstr "Unexpected function."

msgid "errors.unexpected_bracket"
msgstr "Unexpected bracket."

msgid "errors.unexpected_closing_bracket"
msgstr "Unexpected closing bracket."

msgid "errors.missing_closing_bracket"
msgstr "Missing closing bracket."

msgid "errors.unexpected_operator"
msgstr "Unexpected operator."

msgid "errors.unexpected_comma"
msgstr "Unexpected comma."

msgid "errors.unexpected_colon"
msgstr "Unexpected colon."

msgid "errors.unexpected_dot"
msgstr "Unexpected dot."

msgid "errors.unexpected_boolean"
msgstr "Unexpected boolean."

msgid "errors.unexpected_string"
msgstr "Unexpected string."

msgid "errors.unexpected_number"
msgstr "Unexpected number."

msgid "errors.unexpected_variable"
msgstr "Unexpected variable."

msgid "errors.invalid_index"
msgstr "Invalid index."

msgid "errors.unexpected_assignment"
msgstr "Unexpected assignment."

msgid "errors.expected_when_or_else"
msgstr "Expecting a when or an else case."

msgid "errors.only_one_else_allowed"
msgstr "Only one else case is allowed per match."

msgid "errors.when_must_belong_to_match"
msgstr "When statements can only appear as children of match statements."

msgid "errors.concurrent_line_without_origin"
msgstr "Concurrent lines need an origin line that doesn't start with \"| \"."

msgid "errors.goto_not_allowed_on_concurrect_lines"
msgstr "Goto references are not allowed on concurrent dialogue lines."

msgid "errors.unexpected_syntax_on_nested_dialogue_line"
msgstr "Nested dialogue lines may only contain dialogue."

msgid "errors.err_nested_dialogue_invalid_jump"
msgstr "Only the last line of nested dialogue is allowed to include a jump."

msgid "errors.unknown"
msgstr "Unknown syntax."

msgid "update.available"
msgstr "v{version} available"

msgid "update.is_available_for_download"
msgstr "Version %s is available for download!"

msgid "update.downloading"
msgstr "Downloading..."

msgid "update.download_update"
msgstr "Download update"

msgid "update.needs_reload"
msgstr "The project needs to be reloaded to install the update."

msgid "update.reload_ok_button"
msgstr "Reload project"

msgid "update.reload_cancel_button"
msgstr "Do it later"

msgid "update.reload_project"
msgstr "Reload project"

msgid "update.release_notes"
msgstr "Read release notes"

msgid "update.success"
msgstr "Dialogue Manager is now v{version}."

msgid "update.failed"
msgstr "There was a problem downloading the update."

msgid "runtime.no_resource"
msgstr "No dialogue resource provided."

msgid "runtime.no_content"
msgstr "\"{file_path}\" has no content."

msgid "runtime.errors"
msgstr "You have {count} errors in your dialogue text."

msgid "runtime.error_detail"
msgstr "Line {line}: {message}"

msgid "runtime.errors_see_details"
msgstr "You have {count} errors in your dialogue text. See Output for details."

msgid "runtime.invalid_expression"
msgstr "\"{expression}\" is not a valid expression: {error}"

msgid "runtime.array_index_out_of_bounds"
msgstr "Index {index} out of bounds of array \"{array}\"."

msgid "runtime.left_hand_size_cannot_be_assigned_to"
msgstr "Left hand side of expression cannot be assigned to."

msgid "runtime.key_not_found"
msgstr "Key \"{key}\" not found in dictionary \"{dictionary}\""

msgid "runtime.property_not_found"
msgstr "\"{property}\" not found. States with directly referenceable properties/methods/signals include {states}. Autoloads need to be referenced by their name to use their properties."

msgid "runtime.property_not_found_missing_export"
msgstr "\"{property}\" not found. You might need to add an [Export] decorator. States with directly referenceable properties/methods/signals include {states}. Autoloads need to be referenced by their name to use their properties."

msgid "runtime.method_not_found"
msgstr "Method \"{method}\" not found. States with directly referenceable properties/methods/signals include {states}. Autoloads need to be referenced by their name to use their properties."

msgid "runtime.signal_not_found"
msgstr "Signal \"{signal_name}\" not found. States with directly referenceable properties/methods/signals include {states}. Autoloads need to be referenced by their name to use their properties."

msgid "runtime.method_not_callable"
msgstr "\"{method}\" is not a callable method on \"{object}\""

msgid "runtime.unknown_operator"
msgstr "Unknown operator."

msgid "runtime.unknown_autoload"
msgstr "\"{autoload}\" doesn't appear to be a valid autoload."

msgid "runtime.something_went_wrong"
msgstr "Something went wrong."

msgid "runtime.expected_n_got_n_args"
msgstr "\"{method}\" was called with {received} arguments but it only has {expected}."

msgid "runtime.unsupported_array_type"
msgstr "Array[{type}] isn't supported in mutations. Use Array as a type instead."

msgid "runtime.dialogue_balloon_missing_start_method"
msgstr "Your dialogue balloon is missing a \"start\" or \"Start\" method."

msgid "runtime.top_level_states_share_name"
msgstr "Multiple top-level states ({states}) share method/property/signal name \"{key}\". Only the first occurance is accessible to dialogue."

msgid "translation_plugin.character_name"
msgstr "Character name"