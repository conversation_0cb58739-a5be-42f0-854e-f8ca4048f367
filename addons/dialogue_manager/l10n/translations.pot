msgid ""
msgstr ""
"Project-Id-Version: Dialogue Manager\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8-bit\n"

msgid "start_a_new_file"
msgstr ""

msgid "open_a_file"
msgstr ""

msgid "open.open"
msgstr ""

msgid "open.quick_open"
msgstr ""

msgid "open.no_recent_files"
msgstr ""

msgid "open.clear_recent_files"
msgstr ""

msgid "save_all_files"
msgstr ""

msgid "all"
msgstr ""

msgid "find_in_files"
msgstr ""

msgid "test_dialogue"
msgstr ""

msgid "test_dialogue_from_line"
msgstr ""

msgid "search_for_text"
msgstr ""

msgid "insert"
msgstr ""

msgid "translations"
msgstr ""

msgid "sponsor"
msgstr ""

msgid "show_support"
msgstr ""

msgid "docs"
msgstr ""

msgid "insert.wave_bbcode"
msgstr ""

msgid "insert.shake_bbcode"
msgstr ""

msgid "insert.typing_pause"
msgstr ""

msgid "insert.typing_speed_change"
msgstr ""

msgid "insert.auto_advance"
msgstr ""

msgid "insert.templates"
msgstr ""

msgid "insert.title"
msgstr ""

msgid "insert.dialogue"
msgstr ""

msgid "insert.response"
msgstr ""

msgid "insert.random_lines"
msgstr ""

msgid "insert.random_text"
msgstr ""

msgid "insert.actions"
msgstr ""

msgid "insert.jump"
msgstr ""

msgid "insert.end_dialogue"
msgstr ""

msgid "generate_line_ids"
msgstr ""

msgid "use_uuid_only_for_ids"
msgstr ""

msgid "set_id_prefix_length"
msgstr ""

msgid "id_prefix_length"
msgstr ""

msgid "save_to_csv"
msgstr ""

msgid "import_from_csv"
msgstr ""

msgid "confirm_close"
msgstr ""

msgid "confirm_close.save"
msgstr ""

msgid "confirm_close.discard"
msgstr ""

msgid "buffer.save"
msgstr ""

msgid "buffer.save_as"
msgstr ""

msgid "buffer.close"
msgstr ""

msgid "buffer.close_all"
msgstr ""

msgid "buffer.close_other_files"
msgstr ""

msgid "buffer.copy_file_path"
msgstr ""

msgid "buffer.show_in_filesystem"
msgstr ""

msgid "n_of_n"
msgstr ""

msgid "search.find"
msgstr ""

msgid "search.find_all"
msgstr ""

msgid "search.placeholder"
msgstr ""

msgid "search.replace_placeholder"
msgstr ""

msgid "search.replace_selected"
msgstr ""

msgid "search.previous"
msgstr ""

msgid "search.next"
msgstr ""

msgid "search.match_case"
msgstr ""

msgid "search.toggle_replace"
msgstr ""

msgid "search.replace_with"
msgstr ""

msgid "search.replace"
msgstr ""

msgid "search.replace_all"
msgstr ""

msgid "files_list.filter"
msgstr ""

msgid "titles_list.filter"
msgstr ""

msgid "errors.key_not_found"
msgstr ""

msgid "errors.line_and_message"
msgstr ""

msgid "errors_in_script"
msgstr ""

msgid "errors_with_build"
msgstr ""

msgid "errors.import_errors"
msgstr ""

msgid "errors.already_imported"
msgstr ""

msgid "errors.duplicate_import"
msgstr ""

msgid "errors.unknown_using"
msgstr ""

msgid "errors.empty_title"
msgstr ""

msgid "errors.duplicate_title"
msgstr ""

msgid "errors.invalid_title_string"
msgstr ""

msgid "errors.invalid_title_number"
msgstr ""

msgid "errors.unknown_title"
msgstr ""

msgid "errors.jump_to_invalid_title"
msgstr ""

msgid "errors.title_has_no_content"
msgstr ""

msgid "errors.invalid_expression"
msgstr ""

msgid "errors.unexpected_condition"
msgstr ""

msgid "errors.duplicate_id"
msgstr ""

msgid "errors.missing_id"
msgstr ""

msgid "errors.invalid_indentation"
msgstr ""

msgid "errors.condition_has_no_content"
msgstr ""

msgid "errors.incomplete_expression"
msgstr ""

msgid "errors.invalid_expression_for_value"
msgstr ""

msgid "errors.file_not_found"
msgstr ""

msgid "errors.unexpected_end_of_expression"
msgstr ""

msgid "errors.unexpected_function"
msgstr ""

msgid "errors.unexpected_bracket"
msgstr ""

msgid "errors.unexpected_closing_bracket"
msgstr ""

msgid "errors.missing_closing_bracket"
msgstr ""

msgid "errors.unexpected_operator"
msgstr ""

msgid "errors.unexpected_comma"
msgstr ""

msgid "errors.unexpected_colon"
msgstr ""

msgid "errors.unexpected_dot"
msgstr ""

msgid "errors.unexpected_boolean"
msgstr ""

msgid "errors.unexpected_string"
msgstr ""

msgid "errors.unexpected_number"
msgstr ""

msgid "errors.unexpected_variable"
msgstr ""

msgid "errors.invalid_index"
msgstr ""

msgid "errors.unexpected_assignment"
msgstr ""

msgid "errors.expected_when_or_else"
msgstr ""

msgid "errors.only_one_else_allowed"
msgstr ""

msgid "errors.when_must_belong_to_match"
msgstr ""

msgid "errors.concurrent_line_without_origin"
msgstr ""

msgid "errors.goto_not_allowed_on_concurrect_lines"
msgstr ""

msgid "errors.unexpected_syntax_on_nested_dialogue_line"
msgstr ""

msgid "errors.err_nested_dialogue_invalid_jump"
msgstr ""

msgid "errors.unknown"
msgstr ""

msgid "update.available"
msgstr ""

msgid "update.is_available_for_download"
msgstr ""

msgid "update.downloading"
msgstr ""

msgid "update.download_update"
msgstr ""

msgid "update.needs_reload"
msgstr ""

msgid "update.reload_ok_button"
msgstr ""

msgid "update.reload_cancel_button"
msgstr ""

msgid "update.reload_project"
msgstr ""

msgid "update.release_notes"
msgstr ""

msgid "update.success"
msgstr ""

msgid "update.failed"
msgstr ""

msgid "runtime.no_resource"
msgstr ""

msgid "runtime.no_content"
msgstr ""

msgid "runtime.errors"
msgstr ""

msgid "runtime.error_detail"
msgstr ""

msgid "runtime.errors_see_details"
msgstr ""

msgid "runtime.invalid_expression"
msgstr ""

msgid "runtime.array_index_out_of_bounds"
msgstr ""

msgid "runtime.left_hand_size_cannot_be_assigned_to"
msgstr ""

msgid "runtime.key_not_found"
msgstr ""

msgid "runtime.property_not_found"
msgstr ""

msgid "runtime.property_not_found_missing_export"
msgstr ""

msgid "runtime.method_not_found"
msgstr ""

msgid "runtime.signal_not_found"
msgstr ""

msgid "runtime.method_not_callable"
msgstr ""

msgid "runtime.unknown_operator"
msgstr ""

msgid "runtime.unknown_autoload"
msgstr ""

msgid "runtime.something_went_wrong"
msgstr ""

msgid "runtime.expected_n_got_n_args"
msgstr ""

msgid "runtime.unsupported_array_type"
msgstr ""

msgid "runtime.dialogue_balloon_missing_start_method"
msgstr ""

msgid "runtime.top_level_states_share_name"
msgstr ""

msgid "translation_plugin.character_name"
msgstr ""