# 🖼️ DYNAMICKÉ POZADIA KAPITOLA 2 - PREKLIATE DEDIČSTVO

## ✅ IMPLEMENTOVANÉ DYNAMICKÉ POZADIA

### 🎨 **Pozadia pre Kapitolu 2:**

#### **Dostup<PERSON><PERSON> o<PERSON>r<PERSON><PERSON>ky:**
- `assets/pozadia/Kapitola_2/1.png` - Úvodné pozadie
- `assets/pozadia/Kapitola_2/2.png` - Po úvodných dialógoch
- `assets/pozadia/Kapitola_2/3.png` - Po prvom puzzle
- `assets/Obrázky/UI_Pozadie.png` - Počas puzzle (pergamenové pozadie)

### 🔄 **Logika zmien pozadí:**

#### **Story Phase 0 (Úvod kapitoly):**
- **Pozadie:** `1.png`
- **Stav:** Úvodné dialógy kapitoly
- **Hudba:** Castle Gates

#### **Story Phase 1 (Po úvodných dialógoch):**
- **Pozadie:** `2.png` 
- **Stav:** Zobrazenie prvého puzzle tlačidla
- **Akcia:** Automatická zmena pozadia

#### **Počas prvého puzzle:**
- **Pozadie:** `UI_Pozadie.png`
- **Stav:** Blood Inscription Puzzle aktívny
- **Hudba:** Puzzle Theme

#### **Story Phase 2 (Po prvom puzzle):**
- **Pozadie:** `2.png`
- **Stav:** Interlude dialógy medzi puzzle
- **Akcia:** Návrat na druhé pozadie

#### **Story Phase 3 (Po interlude dialógoch):**
- **Pozadie:** `3.png`
- **Stav:** Zobrazenie druhého puzzle tlačidla
- **Akcia:** Zmena na tretie pozadie

#### **Počas druhého puzzle:**
- **Pozadie:** `UI_Pozadie.png`
- **Stav:** Order Test Puzzle aktívny
- **Hudba:** Puzzle Theme

#### **Po dokončení druhého puzzle:**
- **Pozadie:** `3.png`
- **Stav:** Záverečné dialógy kapitoly
- **Akcia:** Návrat na tretie pozadie

## 🔧 TECHNICKÁ IMPLEMENTÁCIA

### **Aktualizované súbory:**

#### **1. scenes/Chapter2.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_2/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_2

#### **2. scripts/Chapter.gd**

**Zmena pozadia po úvodných dialógoch:**
```gdscript
# Po úvodných dialógoch - ukázať prvý hlavolam
story_phase = 1
# Pre Kapitolu 1 - zmeniť pozadie na druhý obrázok
if chapter_number == 1:
    change_background_image("res://assets/Obrázky/Kapitola_1/2.png")
# Pre Kapitolu 2 - zmeniť pozadie na druhý obrázok
elif chapter_number == 2:
    change_background_image("res://assets/pozadia/Kapitola_2/2.png")
```

**Zmena pozadia po interlude dialógoch:**
```gdscript
# Po dialógoch medzi hlavolamami - ukázať druhý hlavolam
story_phase = 3
# Pre Kapitolu 2 - zmeniť pozadie na tretí obrázok
if chapter_number == 2:
    change_background_image("res://assets/pozadia/Kapitola_2/3.png")
```

**Pozadie počas puzzle:**
```gdscript
func show_puzzle_scene(puzzle_number: int):
    # Pri spustení puzzle vrátiť pozadie na UI_Pozadie.png
    change_background_image("res://assets/Obrázky/UI_Pozadie.png")
```

**Návrat pozadia po puzzle:**
```gdscript
# Po prvom puzzle
if chapter_number == 2:
    # Vrátiť pozadie na druhý obrázok (po prvom puzzle)
    change_background_image("res://assets/pozadia/Kapitola_2/2.png")

# Po druhom puzzle  
if chapter_number == 2:
    # Vrátiť pozadie na tretí obrázok (po druhom puzzle)
    change_background_image("res://assets/pozadia/Kapitola_2/3.png")
```

### **Existujúca funkcia:**
```gdscript
func change_background_image(image_path: String):
    if background:
        var new_texture = load(image_path)
        if new_texture:
            background.texture = new_texture
            print("Pozadie zmenené na: ", image_path)
        else:
            print("CHYBA: Nemožno načítať obrázok: ", image_path)
```

## 🎮 POUŽÍVATEĽSKÁ SKÚSENOSŤ

### **Vizuálny flow Kapitoly 2:**

1. **Spustenie kapitoly** → Pozadie 1 (úvodná scéna)
2. **Úvodné dialógy** → Pozadie 2 (zmena atmosféry)
3. **Prvý puzzle** → UI pozadie (pergamen pre puzzle)
4. **Po prvom puzzle** → Pozadie 2 (návrat)
5. **Interlude dialógy** → Pozadie 3 (pokrok v príbehu)
6. **Druhý puzzle** → UI pozadie (pergamen pre puzzle)
7. **Po druhom puzzle** → Pozadie 3 (záver kapitoly)
8. **Záverečné dialógy** → Pozadie 3 (dokončenie)

### **Výhody dynamických pozadí:**

- ✅ **Vizuálny pokrok** - Pozadia odrážajú pokrok v príbehu
- ✅ **Atmosféra** - Každá fáza má svoju vizuálnu identitu
- ✅ **Konzistentnosť** - Puzzle majú jednotné pergamenové pozadie
- ✅ **Immerzia** - Plynulé vizuálne zmeny podporujú príbeh

## 🧪 TESTOVANIE

### **Test súbory:**
- `scripts/chapter2_background_test.gd` - Test načítania pozadí
- `scenes/Chapter2BackgroundTest.tscn` - Test scéna

### **Testované scenáre:**
- ✅ Načítanie všetkých 3 pozadí z Kapitola_2
- ✅ Načítanie UI_Pozadie.png
- ✅ Správne rozmery a typy textúr
- ✅ Logika zmien pozadí podľa story_phase

### **Spustenie testu:**
```
1. Otvorte scenes/Chapter2BackgroundTest.tscn
2. Spustite scénu (F6)
3. Pozrite konzolu pre výsledky
```

## 📋 POROVNANIE S KAPITOLOU 1

### **Kapitola 1 (existujúca logika):**
- Úvod: `assets/Obrázky/Kapitola_1/1.png` (nastavené v scéne)
- Po dialógoch: `assets/Obrázky/Kapitola_1/2.png`
- Puzzle: `UI_Pozadie.png`

### **Kapitola 2 (nová implementácia):**
- Úvod: `assets/pozadia/Kapitola_2/1.png` (nastavené v scéne)
- Po dialógoch: `assets/pozadia/Kapitola_2/2.png`
- Po prvom puzzle: `assets/pozadia/Kapitola_2/2.png`
- Po interlude: `assets/pozadia/Kapitola_2/3.png`
- Puzzle: `UI_Pozadie.png`

### **Rozšírenie pre ďalšie kapitoly:**
Rovnaká logika sa môže aplikovať na:
- `assets/pozadia/Kapitola_3/` (1.png, 2.png, 3.png)
- `assets/pozadia/Kapitola_4/` (1.png, 2.png, 3.png)
- `assets/pozadia/Kapitola_5/` (1.png, 2.png, 3.png)
- `assets/pozadia/Kapitola_6/` (1.png, 2.png, 3.png)
- `assets/pozadia/Kapitola_7/` (1.png, 2.png, 3.png)

---

**🖼️ Dynamické pozadia pre Kapitolu 2 sú úspešne implementované!**

**Kapitola 2 má teraz bohatú vizuálnu skúsenosť s 3 rôznymi pozadiami podľa pokroku v príbehu! 🏰✨**
