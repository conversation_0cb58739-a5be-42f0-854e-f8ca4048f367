# Opravy pre Kapitolu 3: Pátranie v zámku

## Implementované opravy

### ✅ **1. Automatický prechod do kapitoly 4**
- **Problém**: Z kapitoly 3 sa neprechádzalo plynule do kapitoly 4
- **Riešenie**: Kapitola 3 už má implementované záverečné dialógy a automatický prechod
- **Logika**: 
  - Po vyriešení druhého hlavolamu (Jednoduchý výpočet) sa spustia záverečné dialógy
  - Po dokončení dialógov sa zobrazí dialóg dokončenia kapitoly na 3 sekundy
  - Automaticky sa načíta Kapitola 4

### ✅ **2. Pozadie knižnice pri správnej vete**
- **Problém**: Obrázok knižnice sa mal zmeniť pri vete "Vstupujete do rozľahlej knižnice"
- **Riešenie**: Implementovaný systém detekcie konkrétnych viet v dialógoch
- **Implementácia**:
  - Pridaný signál `background_change_requested` v DialogueSystem
  - Detekcia konkrétnej vety v `display_current_line()`
  - Automatická zmena pozadia pri zobrazení vety

## Technické detaily

### **Zmeny v súboroch:**

#### **1. scripts/DialogueSystem.gd**
```gdscript
# Pridaný signál
signal background_change_requested(image_path: String)

# Detekcia konkrétnej vety
if text == "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_3/3.png")
```

#### **2. scripts/Chapter.gd**
```gdscript
# Pripojenie signálu
dialogue_system.background_change_requested.connect(_on_background_change_requested)

# Spracovanie signálu
func _on_background_change_requested(image_path: String):
    change_background_image(image_path)

# Pridaná zmena pozadia po úvodných dialógoch pre kapitolu 3
elif chapter_number == 3:
    change_background_image("res://assets/pozadia/Kapitola_3/2.png")
```

#### **3. scenes/Chapter3.tscn**
```
# Počiatočné pozadie zostáva 1.png (veľká hala)
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_3/1.png" id="2_bg"]
```

### **Nová logika pozadí:**

| Fáza | Pozadie | Trigger |
|------|---------|---------|
| Úvod | `1.png` | Počiatočné pozadie (veľká hala) |
| Po úvodných dialógoch | `2.png` | Po dokončení úvodných dialógov (pred prvou hádankou) |
| Interlude dialógy | `3.png` | Pri vete "Vstupujete do rozľahlej knižnice" |
| Po interlude dialógoch | `3.png` | Zostáva v knižnici |
| Počas puzzle | `UI_Pozadie.png` | Pri spustení puzzle |

### **Automatický prechod kapitol:**

1. **Dokončenie druhého puzzle** → Záverečné dialógy
2. **Dokončenie záverečných dialógov** → Dialóg dokončenia kapitoly (3s)
3. **Automatický prechod** → Kapitola 4

## Testovanie

Pre overenie opráv:

1. **Spustite Kapitolu 3**
2. **Prejdite úvodné dialógy** - pozadie sa zmení na 2.png (pred prvou hádankou)
3. **Vyriešte prvý hlavolam** - spustia sa interlude dialógy
4. **Sledujte vetu "Vstupujete do rozľahlej knižnice"** - pozadie sa zmení na knižnicu (3.png)
5. **Dokončite interlude dialógy** - pozadie zostane na 3.png
6. **Vyriešte druhý hlavolam** - spustia sa záverečné dialógy
7. **Dokončite záverečné dialógy** - automatický prechod na Kapitolu 4

## Výsledok

- ✅ Pozadie knižnice sa zobrazuje presne pri správnej vete
- ✅ Automatický prechod do kapitoly 4 funguje správne
- ✅ Zachovaná konzistencia s ostatnými kapitolami
- ✅ Žiadne chyby v diagnostike

Oba problémy sú úspešne vyriešené a kapitola 3 teraz funguje podľa požiadaviek.
