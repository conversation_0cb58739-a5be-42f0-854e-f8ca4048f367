[folding]

node_unfolds=[Node<PERSON>ath("."), PackedStringArray("Layout", "Theme"), NodePath("DialoguePanel"), PackedStringArray("Layout"), NodePath("DialoguePanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("DialoguePanel/VBoxContainer/SpeakerLabel"), PackedStringArray("Layout"), NodePath("DialoguePanel/VBoxContainer/TextLabel"), PackedStringArray("Layout"), NodePath("DialoguePanel/VBoxContainer/ContinueButton"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/DialogueSystem.tscn::LabelSettings_speaker", PackedStringArray("Resource", "Font", "Outline", "Shadow")]
nodes_folded=[]
