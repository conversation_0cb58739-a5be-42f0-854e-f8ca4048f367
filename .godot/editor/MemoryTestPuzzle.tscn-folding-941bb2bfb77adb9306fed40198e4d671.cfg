[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout", "Theme"), NodePath("Background"), PackedStringArray("Layout"), NodePath("PuzzlePanel"), PackedStringArray("Layout", "Patch Margin"), NodePath("PuzzlePanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/TitleLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer1"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/DescriptionLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer2"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/StatusLabel"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/Spacer3"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ColorContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ColorContainer/RedButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/ColorContainer/Spacer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ColorContainer/BlueButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/ColorContainer/Spacer2"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ColorContainer/GreenButton"), PackedStringArray("Layout", "Textures"), NodePath("PuzzlePanel/VBoxContainer/Spacer4"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/HintButton"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton"), PackedStringArray("Layout"), NodePath("PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/MemoryTestPuzzle.tscn::LabelSettings_title", PackedStringArray("Resource", "Font", "Outline", "Shadow"), "res://scenes/MemoryTestPuzzle.tscn::LabelSettings_status", PackedStringArray("Resource", "Font", "Outline", "Shadow")]
nodes_folded=[]
