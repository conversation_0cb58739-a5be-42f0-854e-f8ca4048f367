# 🏰 Kapitola 5: <PERSON><PERSON><PERSON><PERSON> - Implementácia

## 📖 **<PERSON>r<PERSON><PERSON><PERSON> kapitoly**

### Scéna 5.1: Zostup do temnôt
- <PERSON>zke kamenné schodisko do hlbín
- <PERSON> strážiť ústup
- Požehnaný kríž od arcibiskupa
- **Hlavolam 9**: <PERSON><PERSON><PERSON> z <PERSON>ňov s číslami na sviečkach

### Scéna 5.2: Pradávna komnata
- Ováln<PERSON> sála s prastarými symbolmi
- Doktorove rozhádzané veci
- Van Helsingove zápisky o Isabelle
- **Hlavolam 10**: Tri páky podľa básničky

## 🎮 **Implementované hlavolamy**

### 🕯️ **Hlavolam 9: K<PERSON><PERSON> z <PERSON>ov**
- **Súbory**: 
  - `scripts/ShadowCodePuzzle.gd`
  - `scenes/ShadowCodePuzzle.tscn`
- **Mechanika**: 
  - 4 s<PERSON><PERSON><PERSON> s číslami: 7, 3, 9, 5
  - <PERSON><PERSON><PERSON>: "<PERSON><PERSON><PERSON><PERSON> páru je tucet"
  - <PERSON><PERSON><PERSON><PERSON> mus<PERSON> z<PERSON> 4-c<PERSON><PERSON><PERSON> kód
- **Riešenie**: 
  - 7+5=12 (tucet), 3+9=12 (tucet)
  - Kód: **7539**

### 🌙 **Hlavolam 10: Tri páky**
- **Súbory**:
  - `scripts/ThreeLeverssPuzzle.gd`
  - `scenes/ThreeLeverssPuzzle.tscn`
- **Mechanika**:
  - Tri páky: Slnko, Mesiac, Hviezda
  - Odkaz: "Kráčaj, kde mesiac svieti, nie tam, kde slnko horí. Hviezda ti ukáže cestu."
  - Sekvenčné klikanie
- **Assety**: `assets/slnko mesiac paky/`
  - `slnko.png` - Slnko (vždy chyba)
  - `mesiac.png` - Mesiac (prvý krok)
  - `hviezda.png` - Hviezda (druhý krok)
- **Riešenie**: Mesiac → Hviezda (Slnko = chyba)

## 💬 **Dialógy**

### Úvodné dialógy
```
"Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín."
"Kamene sú ošúchané tisíckami krokov, no roky tu nik nebol."
"Z hĺbky sála chlad a vlhkosť, pripomínajúce dych hrobky."
"Počkám tu a budem strážiť ústup!"
"Ak sa niečo stane, kričte. A vezmite si toto..."
"Kríž požehnal sám ostrihomský arcibiskup. Môže vám zachrániť život."
```

### Dialógy medzi hlavolamami
```
"Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi."
"V strede stojí kamenný podstavec. Rozhadzané ležia doktorove veci."
"Rozbitá lampa, prázdny strieborný revolver, kožený notes s roztrhanými stranami."
"Na kameňoch tmavnú škvrny, čo nápadne pripomínajú krv. No doktora niet."
"Je tu! Isabelle má prisluhovačov – nie upírov, ale niečo horšie."
"Ako vedela, že prídem? Musím sa dostať k jej sarkofágu..."
"Voda dochádza, striebro tiež... sú ich príliš veľa..."
"Ak toto niekto nájde, dokončite, čo som začal..."
"Za podstavcom odkrývate tajné dvere vedúce do rozľahlej sály."
"Uprostred stojí mohutný sarkofág zdobený tromi pákami."
```

### Záverečné dialógy
```
"Sarkofág sa pomaly otvára s hlbokým kamenným škrípotom."
"Zvnútra sa ozýva slabý vzdych... alebo je to len vietor?"
"Tajomstvo grófky Isabelle Báthoryovej je konečne odhalené."
"Ale to je už príbeh pre ďalšiu kapitolu..."
```

## 🎨 **Dizajn a vizuály**

### Gotická téma
- Aplikovaná `themes/GothicTheme.tres`
- Tmavé farby pre atmosféru krypt
- Konzistentné štýlovanie

### Kód z tieňov
- Emoji sviečky: 🕯️
- Zlaté farby pre čísla
- Fialové farby pre nápis

### Tri páky
- Využité assety z `assets/slnko mesiac paky/`
- TextureButton s ikonami
- Farebné animácie pre feedback

## 🔧 **Technická implementácia**

### GameManager aktualizácie
```gdscript
5: {
    "title": "Kapitola 5: Krypty",
    "description": "Zostup do pradávnych krypt plných tajomstiev a nebezpečenstiev.",
    "puzzles": ["Kód z tieňov", "Tri páky"]
}
```

### Chapter.gd rozšírenia
- Pridané funkcie `show_shadow_code_puzzle()` a `show_three_levers_puzzle()`
- Callback funkcie `_on_shadow_code_solved()` a `_on_three_levers_solved()`
- Rozšírená podpora pre Kapitolu 5

### DialogueSystem.gd rozšírenia
- Pridané dialógy pre Kapitolu 5
- Van Helsingove zápisky
- Nápovedy pre oba hlavolamy

## 🎯 **Kľúčové mechaniky**

### Kód z tieňov
```gdscript
# Kontrola kódu
var correct_code: String = "7539"  # 7+5=12, 3+9=12

func check_code():
    if player_code == correct_code:
        puzzle_solved.emit()
```

### Tri páky
```gdscript
# Správna sekvencia
var correct_sequence: Array[String] = ["moon", "star"]

func _on_sun_pressed():
    # Slnko je vždy chyba
    show_error_feedback()

func _on_moon_pressed():
    if player_sequence.is_empty():
        player_sequence.append("moon")  # Prvý krok

func _on_star_pressed():
    if player_sequence.size() == 1 and player_sequence[0] == "moon":
        puzzle_solved.emit()  # Úspech!
```

## ✅ **Testovanie**

### Kontrolný zoznam
- [ ] Kapitola 5 sa načíta bez chýb
- [ ] Úvodné dialógy sa zobrazujú správne
- [ ] Kód z tieňov prijíma správnu odpoveď (7539)
- [ ] Tri páky fungujú podľa sekvencie (Mesiac → Hviezda)
- [ ] Slnko vždy vyvolá chybu
- [ ] Dialógy medzi hlavolamami sa spúšťajú
- [ ] Van Helsingove zápisky sa zobrazujú
- [ ] Záverečné dialógy sa zobrazujú po dokončení
- [ ] Gotická téma je aplikovaná konzistentne

## 🚀 **Spustenie**

1. Spustite hru v Godot editore
2. Prejdite do menu kapitol
3. Vyberte "Kapitola 5: Krypty"
4. Sledujte úvodné dialógy (Viktor a kríž)
5. Riešte Kód z tieňov (odpoveď: 7539)
6. Sledujte dialógy o Van Helsingových zápiskoch
7. Riešte Tri páky (Mesiac → Hviezda)
8. Sledujte záverečné dialógy o sarkofágu

**Kapitola 5 je pripravená na testovanie!** 🎮✨
