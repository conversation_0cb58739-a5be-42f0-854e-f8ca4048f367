# 🏰 Kapitola 4: <PERSON><PERSON><PERSON> - Implementácia

## 📖 **<PERSON>r<PERSON><PERSON>h kapitoly**

### Scéna 4.1: Chodba plná pascí
- <PERSON><PERSON> krídlo zámku s mechanizmami
- Viktor spomína na básničku o Mesiaci a Slnku
- **Hlavolam 7**: Pamäťový test s farebn<PERSON>mi sekvenciami

### Scéna 4.2: Alchymistické laboratórium  
- Laboratórium plné alchymistických prístrojov
- Recept na ochranný elixír
- **Hlavolam 8**: Vampírska aritmetika s symbolmi

## 🎮 **Implementované hlavolamy**

### 🎨 **Hlavolam 7: <PERSON><PERSON><PERSON>ov<PERSON> test**
- **Súbory**: 
  - `scripts/MemoryTestPuzzle.gd`
  - `scenes/MemoryTestPuzzle.tscn`
- **Mechanika**: 
  - Zobrazí sa náhodná sekvencia 3 farieb
  - <PERSON><PERSON><PERSON><PERSON> musí zopakovať sekvenciu v správnom poradí
  - Použité farebné assety z `assets/Farby/`
- **Assety**:
  - `Cervena_1.png` - Červen<PERSON> tla<PERSON>lo
  - `Modra_1.png` - Modré tlačidlo  
  - `Zelena_1.png` - Zelené tlačidlo
- **Riešenie**: Zapamätať si a zopakovať 3-farebnú sekvenciu

### 🧮 **Hlavolam 8: Vampírska aritmetika**
- **Súbory**:
  - `scripts/VampireArithmeticPuzzle.gd`
  - `scenes/VampireArithmeticPuzzle.tscn`
- **Mechanika**:
  - Tri rovnice s vampírskymi symbolmi
  - 🦇 + 🦇 = 16 (netopier = 8)
  - 🩸 - 🦇 = 3 (krv = 11)
  - ⚰ × 🦇 = 56 (rakva = 7)
  - Otázka: 🦇 + 🩸 + ⚰ = ?
- **Riešenie**: 8 + 11 + 7 = **26**

## 💬 **Dialógy**

### Úvodné dialógy
```
"Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší."
"Steny pokrýva vlhkosť a medzi kameňmi rastú čudné plesne."
"Občas počuť mechanické cvakanie – v múroch stále pracujú skryté mechanizmy."
"Počkajte! Spomínam si na básničku."
"'Kráčaj, kde Mesiac svieti, nie tam, kde Slnko horí.' V núdzi ju vraj použijem."
```

### Dialógy medzi hlavolamami
```
"Úspech! Prešli ste bez spustenia mechanizmov."
"Na konci chodby stoja masívne dubové dvere."
"Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami."
"Stoly sú obsypané sklenenými nádobami, kotlíkmi a čudesnými prístrojmi."
"Toto je recept na ochranný elixír!"
"Doktor mi ho ukázal pred mesiacom."
"Povedal: 'Viktor, ak ma pôjdeš hľadať do katakomb, priprav si tento elixír.'"
```

### Záverečné dialógy
```
"Elixír v fľaštičke jemne zažiari striebristým svetlom."
"Výborne! Teraz sa môžeme odvážiť do katakomb."
"Nezabudnite – elixír chráni len hodinu."
```

## 🎨 **Dizajn a vizuály**

### Gotická téma
- Aplikovaná `themes/GothicTheme.tres`
- Pergamenové farby s outline efektmi
- Konzistentné štýlovanie s ostatnými kapitolami

### Farebné tlačidlá
- Využité existujúce assety z `assets/Farby/`
- TextureButton s farebnými textúrami
- Animácie pre zvýraznenie sekvencie

### Vampírske symboly
- Emoji symboly: 🦇 🩸 ⚰
- Červené farby pre mystickú atmosféru
- Gotické fonty pre nadpisy

## 🔧 **Technická implementácia**

### GameManager aktualizácie
```gdscript
4: {
    "title": "Kapitola 4: Tajné krídlo",
    "description": "Staré krídlo zámku plné pascí a alchymistických tajomstiev.",
    "puzzles": ["Pamäťový test", "Vampírska aritmetika"]
}
```

### Chapter.gd rozšírenia
- Pridané funkcie `show_memory_test_puzzle()` a `show_vampire_arithmetic_puzzle()`
- Callback funkcie `_on_memory_test_solved()` a `_on_vampire_arithmetic_solved()`
- Rozšírená podpora pre Kapitolu 4 v dialógovom systéme

### DialogueSystem.gd rozšírenia
- Pridané dialógy pre Kapitolu 4
- Nápovedy pre oba hlavolamy
- Úspešné a neúspešné správy

## ✅ **Testovanie**

### Kontrolný zoznam
- [ ] Kapitola 4 sa načíta bez chýb
- [ ] Úvodné dialógy sa zobrazujú správne
- [ ] Pamäťový test funguje s farebnými tlačidlami
- [ ] Vampírska aritmetika prijíma správnu odpoveď (26)
- [ ] Dialógy medzi hlavolamami sa spúšťajú
- [ ] Záverečné dialógy sa zobrazujú po dokončení
- [ ] Automatický prechod na Kapitolu 5 (ak existuje)
- [ ] Gotická téma je aplikovaná konzistentne

## 🚀 **Spustenie**

1. Spustite hru v Godot editore
2. Prejdite do menu kapitol
3. Vyberte "Kapitola 4: Tajné krídlo"
4. Sledujte úvodné dialógy
5. Riešte Pamäťový test (zapamätajte si farebné sekvencie)
6. Sledujte dialógy medzi hlavolamami
7. Riešte Vampírsku aritmetiku (odpoveď: 26)
8. Sledujte záverečné dialógy

**Kapitola 4 je pripravená na testovanie!** 🎮✨
