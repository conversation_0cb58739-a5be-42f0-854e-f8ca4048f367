# 🏰 <PERSON><PERSON><PERSON> dizajn pre "Prekliate Dedičstvo"

## 🎨 Farebná paleta

### H<PERSON><PERSON><PERSON> farby
- **Pergamen**: `Color(0.9, 0.85, 0.7, 1)` - <PERSON><PERSON><PERSON>ný text
- **Zlatá**: `Color(1, 0.95, 0.8, 1)` - Hover efekty
- **Tmavohnedá**: `Color(0.8, 0.75, 0.6, 1)` - Pressed stavy
- **Krv**: `Color(0.8, 0.1, 0.1, 1)` - Špeciálne texty (krvavé nápisy)
- **Tieň**: `Color(0.2, 0.1, 0.05, 1)` - Outline a tiene

### Efekty
- **Outline**: 1-2px tmavohnedý
- **Shadow**: 2-3px offset, tmavý tieň
- **Hover**: Svetlejšia verzia základnej farby

## 🖼️ Použité assety

### Buttony
- `assets/Buttons/Buttons_1.png` - Norm<PERSON>lny stav
- `assets/Buttons/Button_Selected.png` - Hover stav  
- `assets/Buttons/Buttons_3.png` - Pressed stav

### Panely a pozadia
- `assets/Scalable screen/Scalable_2.png` - Hlavné panely
- `assets/Scalable screen/Title_Holder.png` - Nadpisy
- `assets/Obrázky/UI_Pozadie.png` - Papierové pozadie

## 📝 Typografia

### Veľkosti fontov
- **Nadpisy**: 24-32px
- **Základný text**: 16-18px
- **Špeciálne texty**: 20px (šifry, krvavé nápisy)

### Štýly
- **Outline**: Všetky texty majú tmavý outline
- **Shadow**: Jemné tiene pre hĺbku
- **Farby**: Teplé, pergamenové tóny

## 🎯 Implementácia

### Téma
Hlavná téma: `themes/GothicTheme.tres`

### Aplikovanie na scény
```gdscript
# V .tscn súboroch
theme = ExtResource("path/to/GothicTheme.tres")
```

### Špeciálne LabelSettings
```gdscript
[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 24
font_color = Color(0.9, 0.8, 0.6, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_size = 1
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)
```

## 🔧 Aktualizované scény

### ✅ Kompletne aktualizované
- `scenes/MainMenu.tscn`
- `scenes/DialogueSystem.tscn`
- `scenes/CaesarCipherPuzzle.tscn`
- `scenes/BloodInscriptionPuzzle.tscn`
- `scenes/Chapter1.tscn`

### ⏳ Potrebujú aktualizáciu
- `scenes/NavigationPuzzle.tscn`
- `scenes/OrderTestPuzzle.tscn`
- `scenes/ReversedMessagePuzzle.tscn`
- `scenes/SimpleCalculationPuzzle.tscn`
- `scenes/Chapter2-6.tscn`
- `scenes/ChaptersMenu.tscn`
- `scenes/AboutGame.tscn`
- `scenes/SettingsMenu.tscn`

## 💡 Odporúčania

1. **Konzistentnosť**: Používajte rovnakú farebnú paletu vo všetkých scénach
2. **Čitateľnosť**: Outline a tiene zlepšujú čitateľnosť na tmavých pozadiach
3. **Atmosféra**: Teplé farby vytvárajú gotickú, ale príjemnú atmosféru
4. **Assety**: Využívajte existujúce textúry pre jednotný vzhľad
