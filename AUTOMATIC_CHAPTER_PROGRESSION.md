# 🎯 AUTOMATIC<PERSON><PERSON> PRECHODY MEDZI KAPITOLAMI - PREKLIATE DEDIČSTVO

## ✅ IMPLEMENTOVANÉ AUTOMATICKÉ PRECHODY

### 🔄 **Systém automatických prechodov:**

#### **Kapitola 1 → Kapitola 2**
- Po dokončení oboch puzzle (Van Helsingova šifra + Navigačná hádanka)
- Záverečné dialógy → Automatický prechod na Kapitolu 2

#### **Kapitola 2 → Kapitola 3**
- Po dokončení oboch puzzle (Krvavý nápis + Test Rádu)
- Záverečné dialógy → Automatický prechod na Kapitolu 3

#### **Kapitola 3 → Kapitola 4**
- Po dokončení oboch puzzle (Obrátená správa + Jednoduchý výpočet)
- Záverečné dialógy → Automatický prechod na Kapitolu 4

#### **Kapitola 4 → Kapitola 5**
- Po dokončení oboch puzzle (<PERSON><PERSON><PERSON><PERSON><PERSON> test + Vampírska aritmetika)
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dial<PERSON> → Automatický prechod na Kapitolu 5

#### **Kapitola 5 → Kapitola 6**
- Po dokončení oboch puzzle (Kód z tieňov + Tri páky)
- Záverečné dialógy → Automatický prechod na Kapitolu 6

#### **Kapitola 6 → Epilóg (Kapitola 7)**
- Po dokončení oboch puzzle (Tri sestry + Rytmus rituálu)
- Záverečné dialógy → **Špeciálny automatický prechod** na Epilóg

#### **Epilóg (Kapitola 7) → Koniec hry**
- Po dokončení epilógu dialógov
- Automatický návrat do **hlavného menu**

## 🔧 TECHNICKÁ IMPLEMENTÁCIA

### **Hlavná logika v `Chapter.gd`:**

```gdscript
func show_chapter_completion():
    await get_tree().create_timer(2.0).timeout
    
    var next_chapter = chapter_number + 1
    var has_next_chapter = next_chapter <= 7 and GameManager.chapter_info.has(next_chapter)
    
    if has_next_chapter:
        # Automatický prechod na ďalšiu kapitolu
        print("✅ Kapitola ", chapter_number, " dokončená - automatický prechod na kapitolu ", next_chapter)
        
        # Krátke zobrazenie úspešného dokončenia (3 sekundy)
        var completion_dialog = AcceptDialog.new()
        # ... dialog setup ...
        
        await get_tree().create_timer(3.0).timeout
        completion_dialog.queue_free()
        
        # Automatický prechod
        GameManager.go_to_chapter(next_chapter)
    else:
        # Koniec hry - návrat do menu
        # ... final dialog ...
```

### **Špeciálny prechod Kapitola 6 → Epilóg:**

```gdscript
func _on_final_dialogue_finished():
    if chapter_number == 6:
        # Pre kapitolu 6 - automaticky prejsť na epilóg
        print("Kapitola 6 dokončená - automatický prechod na epilóg")
        await get_tree().create_timer(2.0).timeout
        GameManager.go_to_chapter(7)
    else:
        # Pre ostatné kapitoly - štandardný automatický prechod
        show_chapter_completion()
```

### **Epilóg dokončenie:**

```gdscript
func _on_dialogue_finished():
    if chapter_number == 7:
        # Pre epilóg - po dialógoch ukončiť hru
        print("Epilóg dokončený - označiť ako dokončený a návrat do hlavného menu")
        GameManager.complete_epilogue()
        await get_tree().create_timer(3.0).timeout
        GameManager.go_to_main_menu()
```

## 🎮 POUŽÍVATEĽSKÁ SKÚSENOSŤ

### **Čo hráč vidí:**

1. **Dokončenie puzzle** → Záverečné dialógy kapitoly
2. **Koniec dialógov** → Krátky dialog "Kapitola dokončená" (3 sekundy)
3. **Automatický prechod** → Načítanie ďalšej kapitoly
4. **Nová kapitola** → Spustenie príslušnej hudby + intro dialógy

### **Výhody automatického systému:**

- ✅ **Plynulý gameplay** - žiadne prerušenia
- ✅ **Lepšia immerzia** - kontinuálny príbeh
- ✅ **Jednoduchosť** - hráč nemusí nič klikať
- ✅ **Konzistentnosť** - rovnaké správanie vo všetkých kapitolách

## 🎵 AUDIO INTEGRÁCIA

### **Automatické spúšťanie hudby:**

```gdscript
func start_chapter_music():
    match chapter_number:
        1: AudioManager.start_chapter_1()    # Storm Journey
        2: AudioManager.start_chapter_2()    # Castle Gates
        3: AudioManager.start_chapter_3()    # Library Secrets
        4: AudioManager.start_chapter_4()    # Alchemy Lab
        5: AudioManager.start_chapter_5()    # Descent Darkness
        6: AudioManager.start_chapter_6()    # Isabelle Awakening
        7: AudioManager.start_epilogue()     # Van Helsing Rescue
```

### **Plynulé audio prechody:**

- **Crossfade efekty** medzi kapitolami (2 sekundy)
- **Puzzle hudba** → návrat k kapitole hudbe
- **Atmosférické zmeny** podľa obsahu kapitoly

## 🧪 TESTOVANIE

### **Test súbory:**
- `scripts/chapter_progression_test.gd` - Logika testovanie
- `scenes/ChapterProgressionTest.tscn` - Test scéna

### **Testované scenáre:**
- ✅ Kapitola 1 → 2 → 3 → 4 → 5 → 6 → 7
- ✅ Epilóg → Hlavné menu
- ✅ Audio prechody medzi kapitolami
- ✅ Správne načítanie chapter_info

## 📋 SÚHRN ZMIEN

### **Upravené súbory:**
- `scripts/Chapter.gd` - Automatické prechody namiesto dialógov s výberom
- `scripts/AudioManager.gd` - Audio triggery pre kapitoly
- Vytvorené test súbory pre overenie funkčnosti

### **Odstránené:**
- Dialógy s výberom "Pokračovať / Menu kapitol"
- Manuálne klikanie na prechod

### **Pridané:**
- Automatické 3-sekundové dialógy s informáciou
- Plynulé prechody medzi kapitolami
- Konzistentné audio prechody

---

**🎯 Automatické prechody medzi kapitolami sú 100% funkčné!**

**Hráč teraz môže hrať kontinuálne od Kapitoly 1 až po Epilóg bez prerušení! 🏰✨**
