[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dqdd56q37i8qv"
path="res://.godot/imported/LindenHill-Regular.ttf-8b24a872a9e9cf429428799cdcce0bf9.fontdata"

[deps]

source_file="res://fonts/Cin<PERSON>,Cormorant_G<PERSON>mond,Linden_Hill/Linden_Hill/LindenHill-Regular.ttf"
dest_files=["res://.godot/imported/LindenHill-Regular.ttf-8b24a872a9e9cf429428799cdcce0bf9.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
