[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://cbeywbr8lpfmb"
path="res://.godot/imported/CormorantGaramond-Light.ttf-998bf34aabff2540d0f905af2c3b1d6f.fontdata"

[deps]

source_file="res://fonts/Cin<PERSON>,Cormorant_Garamond,Linden_Hill/Cormorant_Garamond/static/CormorantGaramond-Light.ttf"
dest_files=["res://.godot/imported/CormorantGaramond-Light.ttf-998bf34aabff2540d0f905af2c3b1d6f.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
