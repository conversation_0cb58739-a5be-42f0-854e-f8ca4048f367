[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://djqbsgc3q475j"
path="res://.godot/imported/Cinzel-VariableFont_wght.ttf-c8868c613baa5bbeb1680bc7795cdeb4.fontdata"

[deps]

source_file="res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/Cinzel-VariableFont_wght.ttf"
dest_files=["res://.godot/imported/Cinzel-VariableFont_wght.ttf-c8868c613baa5bbeb1680bc7795cdeb4.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
