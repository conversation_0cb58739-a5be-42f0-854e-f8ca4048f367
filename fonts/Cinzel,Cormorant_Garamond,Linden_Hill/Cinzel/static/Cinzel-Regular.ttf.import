[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dor2u782fjfyh"
path="res://.godot/imported/Cinzel-Regular.ttf-f7a3301af1d34ec36445b6908998602e.fontdata"

[deps]

source_file="res://fonts/C<PERSON><PERSON>,Cormorant_<PERSON>,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf"
dest_files=["res://.godot/imported/Cinzel-Regular.ttf-f7a3301af1d34ec36445b6908998602e.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
