# Implementácia dynamických pozadí pre Kapitolu 5: Krypty

## ✅ Úspešne implementované

### **Zmeny v súboroch:**

#### **1. scenes/Chapter5.tscn**
- Z<PERSON><PERSON><PERSON> počiatočné pozadie z `UI_Pozadie.png` na `1.png` (kamenné schodisko do hlbín)

#### **2. scripts/Chapter.gd**
- Pridaná logika pre zmenu pozadia po úvodných dialógoch na `2.png`
- Pridaná logika pre návrat pozadia po prvom puzzle na `2.png`
- Pridaná logika pre návrat pozadia po druhom puzzle na `3.png`

#### **3. scripts/DialogueSystem.gd**
- Pridaná detekcia vety "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi"
- Automatická zmena pozadia na `3.png` pri tejto vete

### **Logika pozadí podľa príbehu:**

| Fáza | Pozadie | Trigger | Popis |
|------|---------|---------|-------|
| **Úvod** | `1.png` | Počiatočné pozadie | Kamenné schodisko do hlbín |
| **Po úvodných dialógoch** | `2.png` | Po dokončení úvodných dialógov | Pred prvou hádankou (Kód z tieňov) |
| **Po prvom puzzle** | `2.png` | Návrat po vyriešení puzzle | Pozadie pred prvou hádankou |
| **Interlude dialógy** | `3.png` | Pri vete o oválnej sále | Oválna sála s Van Helsingovými vecami |
| **Po druhom puzzle** | `3.png` | Návrat po vyriešení puzzle | Pozadie pred druhou hádankou |
| **Počas puzzle** | `UI_Pozadie.png` | Pri spustení puzzle | Štandardné pozadie |

### **Naratívna logika:**

1. **1.png**: "Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín"
2. **2.png**: Po úvodných dialógoch s Viktorom a požehnaným krížom
3. **3.png**: "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi"

### **Implementované funkcie:**

```gdscript
# V Chapter.gd - zmena pozadia po úvodných dialógoch
elif chapter_number == 5:
    change_background_image("res://assets/pozadia/Kapitola_5/2.png")

# V Chapter.gd - návrat pozadia po prvom puzzle
if chapter_number == 5:
    change_background_image("res://assets/pozadia/Kapitola_5/2.png")

# V Chapter.gd - návrat pozadia po druhom puzzle
if chapter_number == 5:
    change_background_image("res://assets/pozadia/Kapitola_5/3.png")

# V DialogueSystem.gd - detekcia konkrétnej vety
elif text == "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_5/3.png")
```

### **Automatický prechod kapitol:**

1. **Dokončenie druhého puzzle** → Záverečné dialógy o sarkofágu
2. **Dokončenie záverečných dialógov** → Dialóg dokončenia kapitoly
3. **Stlačenie "Ďalej"** → Okamžitý prechod na Kapitolu 6

## 🎯 **Testovací scenár**

1. **Spustite Kapitolu 5** - pozadie: kamenné schodisko (1.png)
2. **Prejdite úvodné dialógy** - pozadie sa zmení na 2.png
3. **Vyriešte Kód z tieňov** - pozadie sa vráti na 2.png, spustia sa interlude dialógy
4. **Sledujte vetu o oválnej sále** - pozadie sa zmení na oválnu sálu (3.png)
5. **Dokončite interlude dialógy** - pozadie zostane na 3.png
6. **Vyriešte Tri páky** - pozadie sa vráti na 3.png, spustia sa záverečné dialógy
7. **Dokončite záverečné dialógy** - automatický prechod na Kapitolu 6

## 📋 **Kontrolný zoznam**

- ✅ Počiatočné pozadie zmenené na 1.png
- ✅ Zmena pozadia po úvodných dialógoch na 2.png
- ✅ Návrat pozadia po prvom puzzle na 2.png
- ✅ Detekcia vety o oválnej sále implementovaná
- ✅ Automatická zmena pozadia na 3.png pri konkrétnej vete
- ✅ Návrat pozadia po druhom puzzle na 3.png
- ✅ Puzzle používajú štandardné pozadie UI_Pozadie.png
- ✅ Automatický prechod do kapitoly 6 funguje
- ✅ Žiadne chyby v diagnostike
- ✅ Všetky potrebné obrázky sú dostupné

## 🎨 **Dostupné pozadia**

- `assets/pozadia/Kapitola_5/1.png` - Kamenné schodisko do hlbín
- `assets/pozadia/Kapitola_5/2.png` - Miesto pred prvou hádankou
- `assets/pozadia/Kapitola_5/3.png` - Oválna sála s prastarými symbolmi

## 🔗 **Konzistencia so systémom**

Implementácia sleduje rovnakú logiku ako kapitoly 1, 2, 3 a 4:
- Počiatočné pozadie z assets/pozadia/Kapitola_X/1.png
- Zmena pozadia po úvodných dialógoch
- Detekcia konkrétnych viet pre zmenu pozadia
- Návrat pozadia po vyriešení puzzle
- Štandardné pozadie pre puzzle
- Automatický prechod medzi kapitolami

## 🏰 **Špecifické pre Kapitolu 5**

- **Téma**: Zostup do pradávnych krypt
- **Hlavolamy**: Kód z tieňov (7539) a Tri páky (Mesiac → Hviezda)
- **Kľúčová veta**: "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi"
- **Van Helsingove zápisky**: Objavujú sa v interlude dialógoch
- **Hudba**: Špeciálna ancient crypts hudba po dokončení

**Kapitola 5 je pripravená s dynamickými pozadiami!** 🎮🏰✨
