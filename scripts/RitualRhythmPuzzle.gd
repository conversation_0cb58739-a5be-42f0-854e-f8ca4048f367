extends Control
class_name RitualRhythmPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var ritual_label: Label = $PuzzlePanel/VBoxContainer/RitualLabel
@onready var cross_button: TextureButton = $PuzzlePanel/VBoxContainer/SymbolsContainer/CrossButton
@onready var water_button: TextureButton = $PuzzlePanel/VBoxContainer/SymbolsContainer/WaterButton
@onready var fire_button: TextureButton = $PuzzlePanel/VBoxContainer/SymbolsContainer/FireButton
@onready var salt_button: TextureButton = $PuzzlePanel/VBoxContainer/SymbolsContainer/SaltButton
@onready var sequence_label: Label = $PuzzlePanel/VBoxContainer/SequenceLabel
@onready var hint_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array[String] = ["cross", "cross", "water", "water", "cross"]  # Raz-raz-dva-dva-raz
var player_sequence: Array[String] = []
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if cross_button:
		cross_button.pressed.connect(_on_cross_pressed)
	if water_button:
		water_button.pressed.connect(_on_water_pressed)
	if fire_button:
		fire_button.pressed.connect(_on_fire_pressed)
	if salt_button:
		salt_button.pressed.connect(_on_salt_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

func show_puzzle():
	show()
	reset_puzzle()

func reset_puzzle():
	player_sequence.clear()
	hint_level = 0
	update_sequence_display()
	reset_button_states()

func reset_button_states():
	# Nastavenie normálnych farieb
	if cross_button:
		cross_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if water_button:
		water_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if fire_button:
		fire_button.modulate = Color(1.0, 1.0, 1.0, 1.0)
	if salt_button:
		salt_button.modulate = Color(1.0, 1.0, 1.0, 1.0)

func update_sequence_display():
	if sequence_label:
		var display_text = "Sekvencia: "
		for i in range(player_sequence.size()):
			match player_sequence[i]:
				"cross":
					display_text += "✝ "
				"water":
					display_text += "💧 "
				"fire":
					display_text += "🔥 "
				"salt":
					display_text += "🧂 "
		sequence_label.text = display_text

func _on_cross_pressed():
	add_to_sequence("cross")
	animate_button(cross_button)

func _on_water_pressed():
	add_to_sequence("water")
	animate_button(water_button)

func _on_fire_pressed():
	add_to_sequence("fire")
	animate_button(fire_button)

func _on_salt_pressed():
	add_to_sequence("salt")
	animate_button(salt_button)

func animate_button(button: TextureButton):
	if button:
		button.modulate = Color.YELLOW
		var tween = create_tween()
		tween.tween_property(button, "modulate", Color.WHITE, 0.3)

func add_to_sequence(symbol: String):
	player_sequence.append(symbol)
	update_sequence_display()
	
	# Skontroluj, či je sekvencia správna zatiaľ
	var current_index = player_sequence.size() - 1
	if player_sequence[current_index] != correct_sequence[current_index]:
		# Nesprávny symbol
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()
		await get_tree().create_timer(1.5).timeout
		reset_puzzle()
		return

	# Skontroluj, či je sekvencia kompletná
	if player_sequence.size() >= correct_sequence.size():
		# Úspech!
		AudioManager.play_puzzle_success_sound()
		show_success_feedback()
		await get_tree().create_timer(1.5).timeout
		puzzle_solved.emit()
		hide()

func show_success_feedback():
	# Zelené zablikanie všetkých tlačidiel
	var buttons = [cross_button, water_button, fire_button, salt_button]
	for button in buttons:
		if button:
			button.modulate = Color.GREEN
	
	var tween = create_tween()
	for button in buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color.WHITE, 0.5)

func show_error_feedback():
	# Červené zablikanie všetkých tlačidiel
	var buttons = [cross_button, water_button, fire_button, salt_button]
	for button in buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	for button in buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "'Raz' a 'dva' označujú poradie symbolov."
		2:
			return "'Raz' = prvý symbol (kríž), 'dva' = druhý symbol (voda)."
		3:
			return "Stlačte: prvý, prvý, druhý, druhý, prvý."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(text: String):
	# Jednoduchý dialóg s nápoveďou
	if sequence_label:
		var original_text = sequence_label.text
		sequence_label.text = "Nápoveda: " + text
		await get_tree().create_timer(3.0).timeout
		sequence_label.text = original_text

func _on_reset_pressed():
	reset_puzzle()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
