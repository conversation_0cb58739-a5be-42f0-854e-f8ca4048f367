extends Control

# Button references
@onready var nova_hra_button = $MenuContainer/ButtonContainer/NovaHraButton
@onready var kapitoly_button = $MenuContainer/ButtonContainer/KapitolyButton
@onready var nastavenia_button = $MenuContainer/ButtonContainer/NastaveniaButton
@onready var o_hre_button = $MenuContainer/ButtonContainer/OHreButton

# Background and logo
@onready var background = $Background
@onready var logo = $MenuContainer/LogoContainer/Logo

func _ready():
	setup_background()
	setup_logo()
	setup_buttons()
	setup_audio()
	adapt_to_screen_size()
	setup_logo_interactions()

func setup_background():
	# Nastav pozadie
	background.texture = load("res://assets/MENU.png")
	background.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
	background.anchor_left = 0
	background.anchor_top = 0
	background.anchor_right = 1
	background.anchor_bottom = 1

func setup_logo():
	# Nastav logo s vä<PERSON><PERSON><PERSON> veľ<PERSON>
	logo.texture = load("res://assets/logo.png")
	logo.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	logo.custom_minimum_size = Vector2(600, 220)  # Zväčšené logo

	# Spusti animáciu loga
	animate_logo_entrance()

func setup_buttons():
	# Nastav texty buttonov
	nova_hra_button.text = "NOVÁ HRA"
	kapitoly_button.text = "KAPITOLY"
	nastavenia_button.text = "NASTAVENIA"
	o_hre_button.text = "O HRE"

	# Aplikuj rámček na všetky buttony
	var ramcek_texture = load("res://assets/RAMCEK.png")

	for button in [nova_hra_button, kapitoly_button, nastavenia_button, o_hre_button]:
		setup_button_style(button, ramcek_texture)

	# Pripoj signály
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

	# Nastaviť fokus na prvé tlačidlo
	nova_hra_button.grab_focus()

func setup_button_style(button: Button, ramcek_texture: Texture2D):
	# Vytvor StyleBoxTexture pre rámček
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = ramcek_texture
	style_normal.texture_margin_left = 16
	style_normal.texture_margin_top = 16
	style_normal.texture_margin_right = 16
	style_normal.texture_margin_bottom = 16

	var style_hover = StyleBoxTexture.new()
	style_hover.texture = ramcek_texture
	style_hover.texture_margin_left = 16
	style_hover.texture_margin_top = 16
	style_hover.texture_margin_right = 16
	style_hover.texture_margin_bottom = 16
	style_hover.modulate_color = Color(1.2, 1.2, 1.2)

	var style_pressed = StyleBoxTexture.new()
	style_pressed.texture = ramcek_texture
	style_pressed.texture_margin_left = 16
	style_pressed.texture_margin_top = 16
	style_pressed.texture_margin_right = 16
	style_pressed.texture_margin_bottom = 16
	style_pressed.modulate_color = Color(0.8, 0.8, 0.8)

	# Aplikuj štýly na button
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_pressed)

	# Nastav veľkosť buttonu
	button.custom_minimum_size = Vector2(300, 60)

	# Aplikuj font - Menu položky → Cinzel-Regular.ttf
	FontLoader.apply_menu_buttons_font(button)
	button.add_theme_font_size_override("font_size", 20)

	# Centruj text
	button.alignment = HORIZONTAL_ALIGNMENT_CENTER

func setup_audio():
	# Spusti main menu hudbu
	if AudioManager:
		AudioManager.play_music("main_menu")

func adapt_to_screen_size():
	var screen_size = get_viewport().get_visible_rect().size

	if screen_size.x < 600:  # Small phone
		logo.custom_minimum_size = Vector2(400, 140)  # Zväčšené aj pre malé obrazovky
		for button in get_buttons():
			button.custom_minimum_size = Vector2(250, 50)
			button.add_theme_font_size_override("font_size", 16)

	elif screen_size.x > 1200:  # Large screen
		logo.custom_minimum_size = Vector2(700, 280)  # Ešte väčšie pre veľké obrazovky
		for button in get_buttons():
			button.custom_minimum_size = Vector2(400, 80)
			button.add_theme_font_size_override("font_size", 24)

func get_buttons() -> Array:
	return [nova_hra_button, kapitoly_button, nastavenia_button, o_hre_button]

# Button callbacks
func _on_nova_hra_pressed():
	print("Starting new game...")
	AudioManager.play_menu_button_sound()
	start_new_game_with_fade()

func _on_kapitoly_pressed():
	print("Opening chapter selection...")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/ChaptersMenu.tscn")

func _on_nastavenia_pressed():
	print("Opening settings...")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/AudioSettings.tscn")

func _on_o_hre_pressed():
	print("Opening about...")
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/AboutGame.tscn")

func start_new_game_with_fade():
	"""Spustí novú hru s fade efektom a zvukom začiatku"""
	# Vytvor fade overlay
	var fade_overlay = ColorRect.new()
	fade_overlay.color = Color.BLACK
	fade_overlay.color.a = 0.0
	fade_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(fade_overlay)

	# Fade to black
	var tween = create_tween()
	tween.tween_property(fade_overlay, "color:a", 1.0, 1.0)
	await tween.finished

	# Prehrať zvuk začiatku počas fade
	AudioManager.play_game_start_sound()

	# Krátka pauza pre zvuk
	await get_tree().create_timer(0.5).timeout

	# Načítať novú scénu
	get_tree().change_scene_to_file("res://scenes/Chapter1.tscn")

func animate_logo_entrance():
	"""Krásna animácia vstupu loga s efektmi"""
	# Začni s neviditeľným logom
	logo.modulate.a = 0.0
	logo.scale = Vector2(0.3, 0.3)
	logo.rotation = -0.2

	# Vytvor tween pre komplexnú animáciu
	var tween = create_tween()
	tween.set_parallel(true)

	# Fade in efekt
	tween.tween_property(logo, "modulate:a", 1.0, 1.5)
	tween.tween_method(animate_logo_scale_bounce, 0.3, 1.0, 1.5)
	tween.tween_property(logo, "rotation", 0.0, 1.2)

	# Počkaj na dokončenie základnej animácie
	await tween.finished

	# Spusti kontinuálnu animáciu dychu
	start_logo_breathing_animation()

func animate_logo_scale_bounce(scale_value: float):
	"""Animácia s bounce efektom pre škálovanie"""
	# Pridaj bounce efekt
	var bounce_factor = 1.0 + sin(scale_value * PI * 3) * 0.1 * (1.0 - scale_value)
	logo.scale = Vector2(scale_value * bounce_factor, scale_value * bounce_factor)

func start_logo_breathing_animation():
	"""Kontinuálna jemná animácia dychu loga"""
	var breathing_tween = create_tween()
	breathing_tween.set_loops()
	breathing_tween.set_parallel(true)

	# Jemné škálovanie (dýchanie)
	breathing_tween.tween_method(animate_breathing_scale, 1.0, 1.05, 2.0)
	breathing_tween.tween_method(animate_breathing_scale, 1.05, 1.0, 2.0)

	# Jemné svietenie
	breathing_tween.tween_property(logo, "modulate", Color(1.2, 1.1, 0.9, 1.0), 2.0)
	breathing_tween.tween_property(logo, "modulate", Color(1.0, 1.0, 1.0, 1.0), 2.0)

func animate_breathing_scale(scale_value: float):
	"""Jemná animácia dýchania"""
	logo.scale = Vector2(scale_value, scale_value)

func animate_logo_hover_effect():
	"""Efekt pri hover nad logom (voliteľné)"""
	var hover_tween = create_tween()
	hover_tween.set_parallel(true)

	hover_tween.tween_property(logo, "scale", Vector2(1.1, 1.1), 0.3)
	hover_tween.tween_property(logo, "modulate", Color(1.3, 1.2, 0.8, 1.0), 0.3)

func animate_logo_normal():
	"""Návrat loga do normálneho stavu"""
	var normal_tween = create_tween()
	normal_tween.set_parallel(true)

	normal_tween.tween_property(logo, "scale", Vector2(1.0, 1.0), 0.3)
	normal_tween.tween_property(logo, "modulate", Color(1.0, 1.0, 1.0, 1.0), 0.3)

func setup_logo_interactions():
	"""Nastavenie interaktívnych efektov pre logo"""
	# Pridaj hover efekty pre logo
	logo.mouse_entered.connect(_on_logo_mouse_entered)
	logo.mouse_exited.connect(_on_logo_mouse_exited)

	# Umožni logo reagovať na myš
	logo.mouse_filter = Control.MOUSE_FILTER_PASS

func _on_logo_mouse_entered():
	"""Efekt pri navedení myši na logo"""
	animate_logo_hover_effect()

func _on_logo_mouse_exited():
	"""Efekt pri odvedení myši z loga"""
	animate_logo_normal()
