@tool
extends EditorScript

# Skript na aktualizáciu zvyšn<PERSON>ch scén s gotickou témou
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Aktualizujem zvyšné scény s gotickou témou ===")
	
	# Zoznam scén na aktualizáciu
	var scenes_to_update = [
		"res://scenes/Chapter2.tscn",
		"res://scenes/Chapter3.tscn", 
		"res://scenes/Chapter4.tscn",
		"res://scenes/Chapter5.tscn",
		"res://scenes/Chapter6.tscn",
		"res://scenes/ChaptersMenu.tscn",
		"res://scenes/AboutGame.tscn",
		"res://scenes/SettingsMenu.tscn",
		"res://scenes/NavigationPuzzle.tscn",
		"res://scenes/OrderTestPuzzle.tscn",
		"res://scenes/ReversedMessagePuzzle.tscn",
		"res://scenes/SimpleCalculationPuzzle.tscn"
	]
	
	for scene_path in scenes_to_update:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem: ", scene_path)
			add_theme_to_scene(scene_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	print("=== Hotovo! ===")

func add_theme_to_scene(scene_path: String):
	# Načítaj súbor ako text
	var file = FileAccess.open(scene_path, FileAccess.READ)
	if file == null:
		print("Chyba pri otváraní súboru: ", scene_path)
		return
	
	var content = file.get_as_text()
	file.close()
	
	# Skontroluj, či už má tému
	if "GothicTheme.tres" in content:
		print("Scéna už má gotickú tému: ", scene_path)
		return
	
	# Pridaj import pre tému
	var theme_import = '[ext_resource type="Theme" path="res://themes/GothicTheme.tres" id="theme_gothic"]'
	
	# Nájdi riadok s load_steps a zvýš číslo
	var load_steps_regex = RegEx.new()
	load_steps_regex.compile(r'load_steps=(\d+)')
	var result = load_steps_regex.search(content)
	
	if result:
		var current_steps = result.get_string(1).to_int()
		var new_steps = current_steps + 1
		content = content.replace("load_steps=" + str(current_steps), "load_steps=" + str(new_steps))
	
	# Pridaj import na koniec ext_resource sekcií
	var last_ext_resource_pos = content.rfind('[ext_resource')
	if last_ext_resource_pos != -1:
		var line_end = content.find('\n', last_ext_resource_pos)
		if line_end != -1:
			content = content.insert(line_end + 1, theme_import + '\n')
	
	# Pridaj theme do root uzla
	var node_regex = RegEx.new()
	node_regex.compile(r'(\[node name="[^"]*" type="Control"\]\s*\n(?:[^\[]*\n)*?)(\[)')
	var node_result = node_regex.search(content)
	
	if node_result:
		var node_section = node_result.get_string(1)
		if not "theme =" in node_section:
			var insert_pos = node_section.rfind('\n')
			if insert_pos != -1:
				var new_node_section = node_section.insert(insert_pos, '\ntheme = ExtResource("theme_gothic")')
				content = content.replace(node_section, new_node_section)
	
	# Uložiť zmeny
	file = FileAccess.open(scene_path, FileAccess.WRITE)
	if file:
		file.store_string(content)
		file.close()
		print("Téma pridaná do: ", scene_path)
	else:
		print("Chyba pri ukladaní: ", scene_path)
