extends Control

# AudioSettings - Menu pre nastavenie hlasitosti audio

@onready var music_slider = $VBoxContainer/MusicContainer/MusicSlider
@onready var sfx_slider = $VBoxContainer/SFXContainer/SFXSlider
@onready var ui_slider = $VBoxContainer/UIContainer/UISlider

@onready var music_value = $VBoxContainer/MusicContainer/MusicValue
@onready var sfx_value = $VBoxContainer/SFXContainer/SFXValue
@onready var ui_value = $VBoxContainer/UIContainer/UIValue

@onready var title_label = $VBoxContainer/TitleLabel
@onready var test_button = $VBoxContainer/ButtonContainer/TestButton
@onready var back_button = $VBoxContainer/ButtonContainer/BackButton

var test_track_index = 0
var test_tracks = ["main_menu", "storm_journey", "castle_gates", "puzzle_theme"]

func _ready():
	print("🎛️ AudioSettings menu načítané")
	
	# Aplikovanie gotických fontov
	apply_gothic_fonts()
	
	# Načítanie aktuálnych hodnôt
	load_current_values()
	
	# Pripojenie signálov
	connect_signals()

func apply_gothic_fonts():
	"""Aplikuje gotické fonty na UI elementy"""
	FontLoader.apply_font_style(title_label, "chapter_title")
	FontLoader.apply_font_style(test_button, "ui_elements")
	FontLoader.apply_font_style(back_button, "ui_elements")

func load_current_values():
	"""Načíta aktuálne audio nastavenia"""
	music_slider.value = AudioManager.get_music_volume()
	sfx_slider.value = AudioManager.get_sfx_volume()
	ui_slider.value = AudioManager.get_ui_volume()
	
	update_value_labels()

func connect_signals():
	"""Pripojí signály pre slidery a tlačidlá"""
	music_slider.value_changed.connect(_on_music_volume_changed)
	sfx_slider.value_changed.connect(_on_sfx_volume_changed)
	ui_slider.value_changed.connect(_on_ui_volume_changed)
	
	test_button.pressed.connect(_on_test_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

func update_value_labels():
	"""Aktualizuje percentuálne hodnoty"""
	music_value.text = str(int(music_slider.value * 100)) + "%"
	sfx_value.text = str(int(sfx_slider.value * 100)) + "%"
	ui_value.text = str(int(ui_slider.value * 100)) + "%"

func _on_music_volume_changed(value: float):
	"""Zmena hlasitosti hudby"""
	AudioManager.set_music_volume(value)
	update_value_labels()
	print("🎵 Hudba nastavená na: ", int(value * 100), "%")

func _on_sfx_volume_changed(value: float):
	"""Zmena hlasitosti SFX"""
	AudioManager.set_sfx_volume(value)
	update_value_labels()
	print("🔊 SFX nastavené na: ", int(value * 100), "%")

func _on_ui_volume_changed(value: float):
	"""Zmena hlasitosti UI"""
	AudioManager.set_ui_volume(value)
	update_value_labels()
	print("🔘 UI zvuky nastavené na: ", int(value * 100), "%")

func _on_test_button_pressed():
	"""Otvoriť test scénu pre všetky tracky"""
	print("🎵 Otváram test scénu")
	get_tree().change_scene_to_file("res://scenes/AudioTestScene.tscn")

func _on_back_button_pressed():
	"""Návrat do hlavného menu"""
	print("🔙 Návrat do hlavného menu")
	AudioManager.play_music("main_menu")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")
