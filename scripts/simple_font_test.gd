extends Node

# Vyle<PERSON><PERSON><PERSON><PERSON> test fontov s FontVariation systémom

func _ready():
	print("=== TESTOVANIE VYLEPŠENÝCH FONTOV ===")
	test_font_creation()

func test_font_creation():
	print("Testovanie vytvárania FontVariation fontov...")

	# Test FontLoader
	var font_loader = preload("res://scripts/font_loader.gd")

	# Test vytvorenia chapter_title fontu (bold)
	print("\n--- CHAPTER TITLE FONT ---")
	var chapter_font = font_loader.create_font_variation("chapter_title")
	print("✅ Chapter font vytvorený: ", chapter_font != null)
	if chapter_font:
		print("Font typ: ", chapter_font.get_class())
		print("Base font names: ", chapter_font.base_font.font_names)
		print("Embolden (bold): ", chapter_font.variation_embolden)
		print("Transform (italic): ", chapter_font.variation_transform)

	# Test vytvorenia narrator fontu (italic)
	print("\n--- NARRATOR FONT (ITALIC) ---")
	var narrator_font = font_loader.create_font_variation("narrator_text")
	print("✅ Narrator font vytvorený: ", narrator_font != null)
	if narrator_font:
		print("Font typ: ", narrator_font.get_class())
		print("Base font names: ", narrator_font.base_font.font_names)
		print("Embolden: ", narrator_font.variation_embolden)
		print("Transform (italic): ", narrator_font.variation_transform)

	# Test vytvorenia dialogue fontu (normal)
	print("\n--- CHARACTER DIALOGUE FONT ---")
	var dialogue_font = font_loader.create_font_variation("character_dialogue")
	print("✅ Dialogue font vytvorený: ", dialogue_font != null)
	if dialogue_font:
		print("Font typ: ", dialogue_font.get_class())
		print("Base font names: ", dialogue_font.base_font.font_names)

	# Test vytvorenia UI fontu (medium weight)
	print("\n--- UI ELEMENTS FONT ---")
	var ui_font = font_loader.create_font_variation("ui_elements")
	print("✅ UI font vytvorený: ", ui_font != null)
	if ui_font:
		print("Font typ: ", ui_font.get_class())
		print("Base font names: ", ui_font.base_font.font_names)
		print("Embolden (medium): ", ui_font.variation_embolden)

	# Test vytvorenia puzzle fontu (bold)
	print("\n--- PUZZLE TEXT FONT ---")
	var puzzle_font = font_loader.create_font_variation("puzzle_text")
	print("✅ Puzzle font vytvorený: ", puzzle_font != null)
	if puzzle_font:
		print("Font typ: ", puzzle_font.get_class())
		print("Base font names: ", puzzle_font.base_font.font_names)

	print("\n=== TEST DOKONČENÝ ===")
	print("Všetky fonty úspešne vytvorené s FontVariation systémom!")

	# Ukončiť po 8 sekundách
	await get_tree().create_timer(8.0).timeout
	print("Test ukončený.")
	get_tree().quit()
