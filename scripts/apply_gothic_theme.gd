@tool
extends EditorScript

# Skript na aplikovanie gotickej témy na všetky puzzle scény
# Spustite v Godot editore cez Tools > Execute Script

func _run():
	print("=== Aplikujem gotickú tému na puzzle scény ===")
	
	var puzzle_scenes = [
		"res://scenes/BloodInscriptionPuzzle.tscn",
		"res://scenes/NavigationPuzzle.tscn", 
		"res://scenes/OrderTestPuzzle.tscn",
		"res://scenes/ReversedMessagePuzzle.tscn",
		"res://scenes/SimpleCalculationPuzzle.tscn"
	]
	
	var theme_path = "res://themes/GothicTheme.tres"
	
	for scene_path in puzzle_scenes:
		if FileAccess.file_exists(scene_path):
			print("Aktualizujem: ", scene_path)
			update_puzzle_scene(scene_path, theme_path)
		else:
			print("Súbor neexistuje: ", scene_path)
	
	print("=== Hotovo! ===")

func update_puzzle_scene(scene_path: String, theme_path: String):
	var scene = load(scene_path)
	if scene == null:
		print("Chyba pri načítaní scény: ", scene_path)
		return
	
	var instance = scene.instantiate()
	if instance == null:
		print("Chyba pri vytvorení inštancie: ", scene_path)
		return
	
	# Aplikuj tému na root uzol
	if instance.has_method("set_theme"):
		var theme = load(theme_path)
		instance.set_theme(theme)
		print("Téma aplikovaná na: ", scene_path)
	
	# Uložiť zmeny
	var packed_scene = PackedScene.new()
	packed_scene.pack(instance)
	ResourceSaver.save(packed_scene, scene_path)
	
	instance.queue_free()
