extends Node

# Test automatických prechodov medzi kapitolami

func _ready():
	print("🎯 === TEST AUTOMATICKÝCH PRECHODOV ===")
	test_chapter_progression_logic()

func test_chapter_progression_logic():
	print("Testovanie logiky prechodov medzi kapitolami...")
	
	# Test 1: Kapitola 1 → 2
	print("\n--- TEST 1: Kapitola 1 → 2 ---")
	test_next_chapter_logic(1, 2, true)
	
	# Test 2: Kapitola 2 → 3
	print("\n--- TEST 2: Kapitola 2 → 3 ---")
	test_next_chapter_logic(2, 3, true)
	
	# Test 3: Kapitola 3 → 4
	print("\n--- TEST 3: Kapitola 3 → 4 ---")
	test_next_chapter_logic(3, 4, true)
	
	# Test 4: Kapitola 4 → 5
	print("\n--- TEST 4: Kapitola 4 → 5 ---")
	test_next_chapter_logic(4, 5, true)
	
	# Test 5: Kapitola 5 → 6
	print("\n--- TEST 5: Kapitola 5 → 6 ---")
	test_next_chapter_logic(5, 6, true)
	
	# Test 6: Kapitola 6 → 7 (Epi<PERSON>óg)
	print("\n--- TEST 6: Kapitola 6 → 7 (Epilóg) ---")
	test_next_chapter_logic(6, 7, true)
	
	# Test 7: Kapitola 7 (Epilóg) → Koniec
	print("\n--- TEST 7: Kapitola 7 (Epilóg) → Koniec ---")
	test_next_chapter_logic(7, 8, false)  # Nemá ďalšiu kapitolu
	
	print("\n🎯 === VŠETKY TESTY DOKONČENÉ ===")
	
	# Ukončiť po 5 sekundách
	await get_tree().create_timer(5.0).timeout
	get_tree().quit()

func test_next_chapter_logic(current: int, expected_next: int, should_have_next: bool):
	"""Testuje logiku prechodu z aktuálnej kapitoly na ďalšiu"""
	
	var next_chapter = current + 1
	var has_next_chapter = next_chapter <= 7 and GameManager.chapter_info.has(next_chapter)
	
	print("Aktuálna kapitola: ", current)
	print("Očakávaná ďalšia: ", expected_next)
	print("Má ďalšiu kapitolu: ", has_next_chapter)
	print("Očakáva sa ďalšia: ", should_have_next)
	
	if has_next_chapter == should_have_next:
		print("✅ Test prešiel")
		if has_next_chapter:
			print("   → Automatický prechod na kapitolu ", next_chapter)
		else:
			print("   → Koniec hry - návrat do menu")
	else:
		print("❌ Test zlyhal")
	
	# Test existencie kapitoly v GameManager
	if GameManager.chapter_info.has(current):
		print("✅ Kapitola ", current, " existuje v GameManager")
		print("   Názov: ", GameManager.chapter_info[current].title)
	else:
		print("❌ Kapitola ", current, " neexistuje v GameManager")
	
	print("---")
