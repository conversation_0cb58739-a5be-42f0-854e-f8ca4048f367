extends Node

# Test dynamických pozadí pre Kapitolu 2

func _ready():
	print("🖼️ === TEST DYNAMICKÝCH POZADÍ KAPITOLA 2 ===")
	test_background_images()

func test_background_images():
	print("Testovanie existencie pozadí pre Kapitolu 2...")
	
	var backgrounds = [
		"res://assets/pozadia/Kapitola_2/1.png",
		"res://assets/pozadia/Kapitola_2/2.png", 
		"res://assets/pozadia/Kapitola_2/3.png"
	]
	
	for i in range(backgrounds.size()):
		var bg_path = backgrounds[i]
		print("\n--- TEST ", i + 1, ": ", bg_path, " ---")
		
		var texture = load(bg_path)
		if texture:
			print("✅ Pozadie úspešne načítané")
			print("   Typ: ", texture.get_class())
			if texture is Texture2D:
				print("   Rozmer: ", texture.get_width(), "x", texture.get_height())
			else:
				print("   ❌ Nie je Texture2D")
		else:
			print("❌ Nemožno načítať pozadie: ", bg_path)
	
	print("\n--- TEST UI POZADIA ---")
	var ui_bg = load("res://assets/Obrázky/UI_Pozadie.png")
	if ui_bg:
		print("✅ UI pozadie úspešne načítané")
		print("   Typ: ", ui_bg.get_class())
		if ui_bg is Texture2D:
			print("   Rozmer: ", ui_bg.get_width(), "x", ui_bg.get_height())
	else:
		print("❌ Nemožno načítať UI pozadie")
	
	print("\n🖼️ === LOGIKA POZADÍ KAPITOLA 2 ===")
	print("Story Phase 0 (Úvod): 1.png")
	print("Story Phase 1 (Po úvodných dialógoch): 2.png")
	print("Story Phase 2 (Po prvom puzzle): 2.png")
	print("Story Phase 3 (Po interlude dialógoch): 3.png")
	print("Počas puzzle: UI_Pozadie.png")
	print("Po dokončení puzzle: Návrat na príslušné pozadie")
	
	print("\n🎯 === TEST DOKONČENÝ ===")
	
	# Ukončiť po 5 sekundách
	await get_tree().create_timer(5.0).timeout
	get_tree().quit()
