extends Control

# UI References - podľa JavaScript ChapterSelection class
@onready var chapter_title = $MainContainer/ChapterTitle
@onready var chapter_subtitle = $MainContainer/ChapterSubtitle
@onready var chapter_preview = $MainContainer/ChapterPreviewContainer/ChapterPreview
@onready var chapter_description = $MainContainer/ChapterPreviewContainer/ChapterDescription
@onready var prev_button = $MainContainer/NavigationContainer/PrevButton
@onready var next_button = $MainContainer/NavigationContainer/NextButton
@onready var start_button = $MainContainer/StartButton
@onready var back_button = $MainContainer/BackButton

# Chapter Selection settings - ako v JavaScript
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "Epilóg"]

# Chapter data
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k <PERSON> He<PERSON>ovmu zámku. Rozlúštite šifru a nájdite správnu cestu cez temný les k hrôzostrašnému sídlu.",
		"unlocked": true,
		"completed": false
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "Vstup do zámku cez masívnu železnú bránu zdobenú heraldickými symbolmi. Vyriešte krvavý nápis a prejdite skúškou Rádu, aby ste sa dostali dovnútra.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Preskúmanie zámockej knižnice plnej pradávnych kníh a tajomstiev. Objavte obrátené posolstvo a vypočítajte Isabellin rok narodenia.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "Preskúmanie tajného krídla zámku s alchymistickým laboratóriom. Absolvujte test pamäte a vyriešte vampírsku aritmetiku.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 5,
		"title": "TEMNÉ KRYPTY",
		"description": "Zostup do pradávnych katakomb pod zámkom. Rozlúštite tieňový kód a nájdite cestu k Isabellinej hrobke cez labyrint kostí.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 6,
		"title": "FINÁLNA KONFRONTÁCIA",
		"description": "Posledná bitka s Isabelle Báthoryovou v jej hrobke. Vyriešte hádanku troch sestier a rituálny rytmus, aby ste ju zastavili navždy.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 7,
		"title": "EPILÓG",
		"description": "Záverečné momenty príbehu. Úsvit nového dňa a návrat do normálneho sveta po víťazstve nad temnotou.",
		"unlocked": false,
		"completed": false
	}
]

func _ready():
	setup_mobile_ui()
	load_chapter_progress()
	connect_signals()
	show()  # Ako v JavaScript - show() metóda

func setup_mobile_ui():
	"""Nastavenie UI pre mobilný layout"""
	# Nastavenie tlačidiel pre mobil
	start_button.text = "SPUSTIŤ KAPITOLU"
	back_button.text = "← SPÄŤ"
	prev_button.text = "◀ PREDCH."
	next_button.text = "ĎALŠIA ▶"

	# Aplikuj RAMCEK styling na všetky tlačidlá
	setup_button_style(start_button)
	setup_button_style(back_button)
	setup_button_style(prev_button)
	setup_button_style(next_button)

	# Nastavenie preview obrázka pre mobil
	chapter_preview.custom_minimum_size = Vector2(280, 280)
	chapter_preview.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED

func show():
	"""Ako v JavaScript - show() metóda"""
	update_chapter_display()

func navigate(direction: int):
	"""Navigácia medzi kapitolami - presne ako v JavaScript"""
	current_chapter_index += direction

	if current_chapter_index < 0:
		current_chapter_index = chapter_data.size() - 1
	elif current_chapter_index >= chapter_data.size():
		current_chapter_index = 0

	update_chapter_display()

func load_chapter_progress():
	# Load from GameManager
	for i in range(chapter_data.size()):
		var chapter_num = i + 1
		chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
		chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly - presne ako v JavaScript"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)

	# Nastavenie titulku ako v JavaScript
	var title_text = "Epilóg" if is_epilogue else "Kapitola " + roman_numerals[current_chapter_index]
	chapter_title.text = title_text

	# Nastavenie podtitulku a popisu
	chapter_subtitle.text = chapter.title
	chapter_description.text = chapter.description

	# Nastavenie preview obrázka
	var preview_path = get_chapter_preview_path(chapter.number)
	if ResourceLoader.exists(preview_path):
		chapter_preview.texture = load(preview_path)
	else:
		chapter_preview.texture = null

	# Nastavenie start tlačidla ako v JavaScript
	start_button.disabled = false
	start_button.modulate = Color.WHITE
	start_button.text = "Spustiť Epilóg" if is_epilogue else "Spustiť Kapitolu"

	# Aplikuj styling
	apply_mobile_styling()

func apply_mobile_styling():
	"""Aplikovanie mobilného stylingu"""
	# Styling pre titulok
	chapter_title.add_theme_font_size_override("font_size", 32)
	chapter_title.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	chapter_title.add_theme_constant_override("outline_size", 3)
	chapter_title.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

	# Styling pre podtitulok
	chapter_subtitle.add_theme_font_size_override("font_size", 22)
	chapter_subtitle.add_theme_color_override("font_color", Color(0.95, 0.9, 0.7))
	chapter_subtitle.add_theme_constant_override("outline_size", 2)
	chapter_subtitle.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.6))

	# Styling pre popis
	chapter_description.add_theme_font_size_override("font_size", 16)
	chapter_description.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6))
	chapter_description.add_theme_constant_override("outline_size", 1)
	chapter_description.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.7))





# === HELPER FUNCTIONS ===

func get_chapter_preview_path(chapter_number: int) -> String:
	# Použiť nové štvorcové obrázky z Kapitoly_VISUALS
	match chapter_number:
		1:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Interior_view_of_ornate_Victorian_horse-drawn_car_514b5c75-3caa-4a98-9002-82c1c9326dc1_0.png"
		2:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_medieval_castle_gate_with_weathered_iron__ff9848cd-5fdb-41fe-b04c-027d685a6e1e_0.png"
		3:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Grand_medieval_great_hall_with_vaulted_stone_ceil_ab110728-e42f-4cb0-b8c7-c695e3b75b82_0.png"
		4:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Ancient_stone_castle_corridor_with_damp_moss-cove_d1335a08-f001-429f-928d-da3a0e7319b3_2.png"
		5:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Narrow_medieval_stone_staircase_descending_into_c_e226c138-860b-4910-8995-a303e5341756_3.png"
		6:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_stone_sarcophagus_cracking_under_tremendo_eda4a822-6964-41b6-87d0-6091ff5eeebd_2.png"
		7:
			return "res://assets/Kapitoly_VISUALS/vlado_13856_Magnificent_sunrise_breaking_through_clearing_sto_ae111b3c-e624-4fb0-b07d-b18319925104_1.png"
		_:
			return ""

func connect_signals():
	"""Pripojenie signálov ako v JavaScript addEventListener"""
	prev_button.pressed.connect(_on_prev_button_pressed)
	next_button.pressed.connect(_on_next_button_pressed)
	start_button.pressed.connect(_on_start_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

func _on_prev_button_pressed():
	"""Ako v JavaScript - navigate(-1)"""
	navigate(-1)

func _on_next_button_pressed():
	"""Ako v JavaScript - navigate(1)"""
	navigate(1)

func _on_start_button_pressed():
	var current_chapter = chapter_data[current_chapter_index]
	if not current_chapter.unlocked:
		AudioManager.play_puzzle_error_sound()
		show_error_message("Táto kapitola je uzamknutá!")
		return

	AudioManager.play_menu_button_sound()
	show_loading_transition()
	await get_tree().create_timer(0.5).timeout
	GameManager.go_to_chapter(current_chapter.number)

func show_error_message(message: String):
	# Create temporary error message
	var error_label = Label.new()
	error_label.text = message
	error_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	error_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	error_label.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	error_label.offset_left = -150
	error_label.offset_right = 150
	error_label.offset_top = -25
	error_label.offset_bottom = 25
	error_label.add_theme_font_size_override("font_size", 18)
	error_label.add_theme_color_override("font_color", Color.RED)
	error_label.add_theme_constant_override("outline_size", 2)
	error_label.add_theme_color_override("font_outline_color", Color.BLACK)

	# Add background
	var error_bg = NinePatchRect.new()
	error_bg.texture = load("res://assets/RAMCEK.png")
	error_bg.patch_margin_left = 16
	error_bg.patch_margin_top = 16
	error_bg.patch_margin_right = 16
	error_bg.patch_margin_bottom = 16
	error_bg.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	error_bg.modulate = Color(0.8, 0.2, 0.2, 0.8)
	error_label.add_child(error_bg)
	error_label.move_child(error_bg, 0)

	add_child(error_label)

	# Animate and remove
	var tween = create_tween()
	tween.tween_property(error_label, "modulate:a", 0.0, 2.0)
	await tween.finished
	error_label.queue_free()

func show_loading_transition():
	# Create loading overlay
	var loading_overlay = ColorRect.new()
	loading_overlay.color = Color(0, 0, 0, 0.8)
	loading_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

	var loading_label = Label.new()
	loading_label.text = "Načítavam kapitolu..."
	loading_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	loading_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	loading_label.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	loading_label.add_theme_font_size_override("font_size", 24)
	loading_label.add_theme_color_override("font_color", Color(0.831, 0.686, 0.216))
	loading_label.add_theme_constant_override("outline_size", 2)
	loading_label.add_theme_color_override("font_outline_color", Color.BLACK)

	loading_overlay.add_child(loading_label)
	add_child(loading_overlay)

	# Fade in
	loading_overlay.modulate.a = 0.0
	var tween = create_tween()
	tween.tween_property(loading_overlay, "modulate:a", 1.0, 0.3)

func _on_back_button_pressed():
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_button_pressed()

# Old card hover functions removed - not needed for single chapter display

func get_chapter_description(chapter_number: int) -> String:
	# Krátke popisy kapitol
	match chapter_number:
		1:
			return "Búrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu."
		2:
			return "Vstup do zámku cez masívnu bránu. Vyriešte krvavý nápis a prejdite skúškou Rádu."
		3:
			return "Pátranie v zámockej knižnici. Objavte obrátené posolstvo a vypočítajte Isabellin rok narodenia."
		4:
			return "Preskúmanie tajného krídla. Test pamäte a vampírska aritmetika v alchymistickom laboratóriu."
		5:
			return "Zostup do pradávnych katakomb. Rozlúštite tieňový kód a nájdite cestu k hrobke."
		6:
			return "Finálna konfrontácia s Isabelle. Vyriešte hádanku troch sestier a rituálny rytmus."
		_:
			return ""

func setup_button_style(button: Button):
	# Apply RAMCEK frame to button
	var ramcek_texture = load("res://assets/RAMCEK.png")

	# Create StyleBoxTexture for normal state
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = ramcek_texture
	style_normal.texture_margin_left = 16
	style_normal.texture_margin_top = 16
	style_normal.texture_margin_right = 16
	style_normal.texture_margin_bottom = 16

	# Create StyleBoxTexture for hover state
	var style_hover = StyleBoxTexture.new()
	style_hover.texture = ramcek_texture
	style_hover.texture_margin_left = 16
	style_hover.texture_margin_top = 16
	style_hover.texture_margin_right = 16
	style_hover.texture_margin_bottom = 16
	style_hover.modulate_color = Color(1.2, 1.2, 1.0)  # Golden tint

	# Apply styles
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_hover)

	# Center text
	button.alignment = HORIZONTAL_ALIGNMENT_CENTER

# Old page indicator and progress indicator functions removed - not needed for single chapter display
