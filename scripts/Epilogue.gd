extends Control

@onready var dialogue_system: DialogueSystem = $DialogueSystem
@onready var epilogue_title: Label = $VBoxContainer/EpilogueTitle
@onready var back_button: Button = $VBoxContainer/BackButton

func _ready():
	if epilogue_title:
		epilogue_title.text = "Epilóg: Nový začiatok"
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
		back_button.grab_focus()
	if dialogue_system:
		dialogue_system.dialogue_finished.connect(_on_dialogue_finished)

	# Spustenie epilógu
	start_epilogue()

func start_epilogue():
	if dialogue_system:
		var epilogue_dialogue = [
			{"speaker": "Rozpr<PERSON><PERSON><PERSON>", "text": "Gratulujeme! Dokončili ste všetky kapitoly Prekliateho dedičstva."},
			{"speaker": "Rozprávač", "text": "<PERSON><PERSON><PERSON><PERSON> zá<PERSON> boli odhalené a prekliatie je zlomené."},
			{"speaker": "<PERSON>oz<PERSON>r<PERSON><PERSON><PERSON>", "text": "<PERSON>rad je teraz váš a môžete v ňom žiť v pokoji."},
			{"speaker": "Rozprávač", "text": "Ale pamätajte... každ<PERSON> dedičstvo má svoje tajomstvá."},
			{"speaker": "Rozprávač", "text": "A možno sa raz vrátite, aby ste objavili ešte viac záhad..."},
			{"speaker": "Rozprávač", "text": "Ďakujeme vám za hranie! Dúfame, že ste si užili svoje dobrodružstvo."}
		]
		dialogue_system.start_dialogue(epilogue_dialogue)

func _on_dialogue_finished():
	# Epilóg je dokončený, hráč môže ísť späť
	pass

func _on_back_pressed():
	GameManager.go_to_chapters()

func _input(event):
	if event.is_action_pressed("ui_cancel") and (not dialogue_system or not dialogue_system.visible):
		_on_back_pressed()
