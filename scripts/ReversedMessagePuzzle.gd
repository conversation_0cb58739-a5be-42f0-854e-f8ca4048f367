extends Control
class_name ReversedMessagePuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var message_label: Label = $PuzzlePanel/VBoxContainer/MessageLabel
@onready var note_label: RichTextLabel = $PuzzlePanel/VBoxContainer/NoteLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: Button = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var hint_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: Button = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_answer: String = "POD VONKAJŠÍMI MÚRMI SCHOVÁ VCHOD"
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if answer_field:
		answer_field.text_submitted.connect(_on_text_submitted)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Van Helsingov denník"
	
	if description_label:
		description_label.text = "[center]Na stole nájdete Van Helsingov denník.[/center]\n\nPosledná stránka obsahuje zvláštnu správu:"
	
	if message_label:
		message_label.text = "DOP IMÍŠJAKNOV IMARÚM ÁVOHCS DOHCV"
		message_label.add_theme_font_size_override("font_size", 18)
		message_label.add_theme_color_override("font_color", Color(0.8, 0.8, 0.2))  # Zlatá farba
	
	if note_label:
		note_label.text = "[center][i]Van Helsingova poznámka:[/i]\n\"V nebezpečenstve píšem každé slovo odzadu.\"[/center]"

func show_puzzle():
	show()
	if answer_field:
		answer_field.grab_focus()

func _on_submit_pressed():
	check_answer()

func _on_text_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field:
		return
	
	var user_answer = normalize_text(answer_field.text)
	var correct_normalized = normalize_text(correct_answer)
	
	if user_answer == correct_normalized:
		# Správna odpoveď
		AudioManager.play_puzzle_success_sound()
		puzzle_solved.emit()
		hide()
	else:
		# Nesprávna odpoveď
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()

func normalize_text(text: String) -> String:
	# Odstránenie diakritiky a normalizácia
	var normalized = text.to_upper().strip_edges()
	
	# Základná náhrada diakritiky
	var replacements = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}
	
	for old_char in replacements:
		normalized = normalized.replace(old_char, replacements[old_char])
	
	return normalized

func show_error_feedback():
	if answer_field:
		answer_field.modulate = Color.RED
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_hint_dialog(hint_text)
	else:
		show_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Van Helsing píše, že píše slová odzadu. Čo to znamená?"
		2:
			return "Skúste čítať každé slovo pozpätku."
		3:
			return "DOP = POD, IMÍŠJAKNOV = VONKAJŠÍMI..."
		_:
			return "Už ste použili všetky nápovedy!"

func show_hint_dialog(hint_text: String):
	var dialog = AcceptDialog.new()
	add_child(dialog)
	dialog.dialog_text = hint_text
	dialog.title = "Nápoveda"
	dialog.popup_centered()
	
	# Automatické odstránenie dialógu
	dialog.confirmed.connect(func(): dialog.queue_free())
	dialog.close_requested.connect(func(): dialog.queue_free())

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
