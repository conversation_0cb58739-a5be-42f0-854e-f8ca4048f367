extends Control

# AudioTestScene - Scéna pre testovanie všetkých audio trackov

@onready var button_container = $ScrollContainer/VBoxContainer/ButtonContainer
@onready var title_label = $ScrollContainer/VBoxContainer/TitleLabel
@onready var info_label = $ScrollContainer/VBoxContainer/InfoLabel
@onready var status_label = $ScrollContainer/VBoxContainer/StatusLabel
@onready var stop_button = $ScrollContainer/VBoxContainer/ControlsContainer/StopButton
@onready var back_button = $ScrollContainer/VBoxContainer/ControlsContainer/BackButton

# Mapovanie trackov s popismi
var track_descriptions = {
	"main_menu": "🏰 Hlavné menu",
	"storm_journey": "⛈️ Kapitola 1: Búrlivá cesta",
	"forest_shadows": "🌲 Kapitola 1: Temný les",
	"castle_gates": "🚪 Kapitola 2: <PERSON>r<PERSON><PERSON> hrôzy",
	"viktor_theme": "👤 <PERSON>'s Theme",
	"library_secrets": "📚 Kapitola 3: Tajomná knižnica",
	"puzzle_theme": "🧩 Puzzle Theme",
	"alchemy_lab": "⚗️ Kapitola 4: Alchýmia",
	"descent_darkness": "⬇️ Kapitola 5: Zostup do katakomb",
	"ancient_crypts": "⚰️ Kapitola 5: Pradávne hrobky",
	"isabelle_awakening": "🧛‍♀️ Kapitola 6: Prebudenie zla",
	"final_ritual": "🔥 Kapitola 6: Posledný rituál",
	"van_helsing_rescue": "🛡️ Epilóg: Záchrana mentora"
}

func _ready():
	print("🎵 AudioTestScene načítaná")
	
	# Aplikovanie gotických fontov
	apply_gothic_fonts()
	
	# Vytvorenie tlačidiel pre všetky tracky
	create_track_buttons()
	
	# Pripojenie signálov
	connect_signals()
	
	# Aktualizácia statusu
	update_status()

func apply_gothic_fonts():
	"""Aplikuje gotické fonty"""
	FontLoader.apply_font_style(title_label, "chapter_title")
	FontLoader.apply_font_style(info_label, "narrator_text")
	FontLoader.apply_font_style(status_label, "ui_elements")

func create_track_buttons():
	"""Vytvorí tlačidlá pre všetky audio tracky"""
	for track_name in track_descriptions.keys():
		var button = Button.new()
		button.text = track_descriptions[track_name]
		button.size_flags_horizontal = Control.SIZE_EXPAND_FILL
		
		# Aplikovanie fontu
		FontLoader.apply_font_style(button, "ui_elements")
		
		# Pripojenie signálu
		button.pressed.connect(_on_track_button_pressed.bind(track_name))
		
		# Pridanie do kontajnera
		button_container.add_child(button)
		
		print("✅ Vytvorené tlačidlo pre: ", track_name)

func connect_signals():
	"""Pripojí signály pre control tlačidlá"""
	stop_button.pressed.connect(_on_stop_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)
	
	# Aplikovanie fontov na control tlačidlá
	FontLoader.apply_font_style(stop_button, "ui_elements")
	FontLoader.apply_font_style(back_button, "ui_elements")

func _on_track_button_pressed(track_name: String):
	"""Spustí vybraný audio track"""
	print("🎵 Spúšťam track: ", track_name)
	AudioManager.play_music(track_name, false)  # Bez crossfade pre rýchle testovanie
	update_status()

func _on_stop_button_pressed():
	"""Zastaví aktuálnu hudbu"""
	print("⏹️ Zastavujem hudbu")
	AudioManager.stop_music(false)
	update_status()

func _on_back_button_pressed():
	"""Návrat do hlavného menu"""
	print("🔙 Návrat do hlavného menu")
	AudioManager.play_music("main_menu")
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func update_status():
	"""Aktualizuje status label"""
	var current_track = AudioManager.get_current_track()
	if current_track == "":
		status_label.text = "Aktuálne hrá: Žiadny track"
	else:
		var description = track_descriptions.get(current_track, current_track)
		status_label.text = "Aktuálne hrá: " + description

func _process(_delta):
	"""Pravidelne aktualizuje status"""
	update_status()
