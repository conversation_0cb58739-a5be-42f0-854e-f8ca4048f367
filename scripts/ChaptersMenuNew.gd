extends Control

# 🎨 KREATÍVNY MOBILNÝ CHAPTERS MENU
# Moderný card-based design s krásnym UI

# UI References - nový mobilný design
@onready var chapter_title = $SafeArea/MainContainer/Header/TitleContainer/ChapterTitle
@onready var chapter_subtitle = $SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ChapterSubtitle
@onready var chapter_preview = $SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/PreviewContainer/ChapterPreview
@onready var chapter_description = $SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ChapterDescription
@onready var status_label = $SafeArea/MainContainer/ContentArea/ChapterCard/CardContent/ProgressIndicator/StatusLabel

# Navigation & Action buttons
@onready var prev_button = $SafeArea/MainContainer/NavigationArea/NavigationContainer/PrevButton
@onready var next_button = $SafeArea/MainContainer/NavigationArea/NavigationContainer/NextButton
@onready var start_button = $SafeArea/MainContainer/NavigationArea/ActionContainer/StartButton
@onready var back_button = $SafeArea/MainContainer/NavigationArea/ActionContainer/BackButton

# Card container for animations
@onready var chapter_card = $SafeArea/MainContainer/ContentArea/ChapterCard

# Chapter data & navigation
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "Epilóg"]

# Chapter data with beautiful descriptions
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu.",
		"unlocked": true,
		"completed": false
	},
	{
		"number": 2,
		"title": "HRADNÉ BRÁNY",
		"description": "Masívne železné brány zámku sa týčia pred vami. Rozlúštite krvavý nápis a prejdite testom Rádu.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Preskúmajte tajomné chodby a sály zámku. Rozlúštite obrátené správy a matematické hádanky.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "Vstúpte do skrytej časti zámku. Test pamäti a upírska aritmetika vás čakajú.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 5,
		"title": "PODZEMNÉ CHODBY",
		"description": "Zostúpte do temných podzemných chodieb. Nájdite cestu cez labyrint k srdcu zámku.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 6,
		"title": "KONFRONTÁCIA",
		"description": "Finálna bitka s Isabelle Báthoryovou. Vyriešte hádanku troch sestier a rituálny rytmus.",
		"unlocked": false,
		"completed": false
	},
	{
		"number": 7,
		"title": "EPILÓG",
		"description": "Úsvit nového dňa. Návrat do normálneho sveta po víťazstve nad temnotou.",
		"unlocked": false,
		"completed": false
	}
]

func _ready():
	setup_mobile_ui()
	load_chapter_progress()
	connect_signals()
	show_chapter_selection()
	animate_entrance()

func setup_mobile_ui():
	"""Nastavenie krásneho mobilného UI"""
	# Aplikuj styling na všetky elementy
	apply_beautiful_styling()
	
	# Nastavenie card animácií
	setup_card_animations()

func apply_beautiful_styling():
	"""Aplikovanie krásneho stylingu pre mobil"""
	# Title styling
	chapter_title.add_theme_font_size_override("font_size", 26)
	chapter_title.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6))
	chapter_title.add_theme_constant_override("outline_size", 2)
	chapter_title.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))
	
	# Subtitle styling
	chapter_subtitle.add_theme_font_size_override("font_size", 20)
	chapter_subtitle.add_theme_color_override("font_color", Color(0.8, 0.7, 0.5))
	chapter_subtitle.add_theme_constant_override("outline_size", 1)
	chapter_subtitle.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.6))
	
	# Description styling
	chapter_description.add_theme_font_size_override("font_size", 14)
	chapter_description.add_theme_color_override("font_color", Color(0.7, 0.6, 0.4))
	chapter_description.add_theme_constant_override("outline_size", 1)
	chapter_description.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.5))
	
	# Status styling
	status_label.add_theme_font_size_override("font_size", 16)
	status_label.add_theme_constant_override("outline_size", 1)
	status_label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.7))

func setup_card_animations():
	"""Nastavenie animácií pre card"""
	# Začni s miernym scale efektom
	chapter_card.scale = Vector2(0.95, 0.95)
	chapter_card.modulate.a = 0.9

func animate_entrance():
	"""Krásna vstupná animácia"""
	# Začni s neviditeľným card
	chapter_card.modulate.a = 0.0
	chapter_card.scale = Vector2(0.8, 0.8)
	chapter_card.position.y += 50
	
	# Animuj vstup
	var tween = create_tween()
	tween.set_parallel(true)
	
	tween.tween_property(chapter_card, "modulate:a", 1.0, 0.6)
	tween.tween_property(chapter_card, "scale", Vector2(1.0, 1.0), 0.6)
	tween.tween_property(chapter_card, "position:y", chapter_card.position.y - 50, 0.6)
	
	tween.tween_callback(start_breathing_animation).set_delay(0.6)

func start_breathing_animation():
	"""Jemná kontinuálna animácia dýchania"""
	var breathing_tween = create_tween()
	breathing_tween.set_loops()
	
	breathing_tween.tween_property(chapter_card, "scale", Vector2(1.02, 1.02), 2.0)
	breathing_tween.tween_property(chapter_card, "scale", Vector2(1.0, 1.0), 2.0)

func show_chapter_selection():
	"""Zobrazenie aktuálnej kapitoly"""
	update_chapter_display()

func navigate(direction: int):
	"""Navigácia s krásnou animáciou"""
	# Animuj prechod
	animate_transition(direction)
	
	# Aktualizuj index
	current_chapter_index += direction
	if current_chapter_index < 0:
		current_chapter_index = chapter_data.size() - 1
	elif current_chapter_index >= chapter_data.size():
		current_chapter_index = 0
	
	# Prehrať zvuk
	AudioManager.play_menu_button_sound()

func animate_transition(direction: int):
	"""Krásna animácia prechodu medzi kapitolami"""
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Slide out effect
	var slide_distance = 100 * direction
	tween.tween_property(chapter_card, "position:x", chapter_card.position.x + slide_distance, 0.3)
	tween.tween_property(chapter_card, "modulate:a", 0.3, 0.3)
	
	await tween.finished
	
	# Update content
	update_chapter_display()
	
	# Reset position and slide in
	chapter_card.position.x -= slide_distance * 2
	
	var tween_in = create_tween()
	tween_in.set_parallel(true)
	tween_in.tween_property(chapter_card, "position:x", chapter_card.position.x + slide_distance, 0.3)
	tween_in.tween_property(chapter_card, "modulate:a", 1.0, 0.3)

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)
	
	# Update title
	var title_text = "EPILÓG" if is_epilogue else "KAPITOLA " + roman_numerals[current_chapter_index]
	chapter_title.text = title_text
	
	# Update content
	chapter_subtitle.text = chapter.title
	chapter_description.text = chapter.description
	
	# Update preview image
	var preview_path = get_chapter_preview_path(chapter.number)
	if ResourceLoader.exists(preview_path):
		chapter_preview.texture = load(preview_path)
	
	# Update status with beautiful colors
	update_status_display(chapter)
	
	# Update start button
	var start_label = start_button.get_node("StartLabel")
	start_label.text = "Spustiť Epilóg" if is_epilogue else "Spustiť Kapitolu"

func update_status_display(chapter):
	"""Aktualizácia statusu s krásnymi farbami"""
	if chapter.completed:
		status_label.text = "✓ DOKONČENÉ"
		status_label.add_theme_color_override("font_color", Color(0.3, 0.8, 0.3))
	elif chapter.unlocked:
		status_label.text = "⚡ DOSTUPNÉ"
		status_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2))
	else:
		status_label.text = "🔒 UZAMKNUTÉ"
		status_label.add_theme_color_override("font_color", Color(0.6, 0.6, 0.6))

func get_chapter_preview_path(chapter_number: int) -> String:
	"""Cesty k novým štvorcovým obrázkom"""
	match chapter_number:
		1: return "res://assets/Kapitoly_VISUALS/vlado_13856_Interior_view_of_ornate_Victorian_horse-drawn_car_514b5c75-3caa-4a98-9002-82c1c9326dc1_0.png"
		2: return "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_medieval_castle_gate_with_weathered_iron__ff9848cd-5fdb-41fe-b04c-027d685a6e1e_0.png"
		3: return "res://assets/Kapitoly_VISUALS/vlado_13856_Grand_medieval_great_hall_with_vaulted_stone_ceil_ab110728-e42f-4cb0-b8c7-c695e3b75b82_0.png"
		4: return "res://assets/Kapitoly_VISUALS/vlado_13856_Ancient_stone_castle_corridor_with_damp_moss-cove_d1335a08-f001-429f-928d-da3a0e7319b3_2.png"
		5: return "res://assets/Kapitoly_VISUALS/vlado_13856_Narrow_medieval_stone_staircase_descending_into_c_e226c138-860b-4910-8995-a303e5341756_3.png"
		6: return "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_stone_sarcophagus_cracking_under_tremendo_eda4a822-6964-41b6-87d0-6091ff5eeebd_2.png"
		7: return "res://assets/Kapitoly_VISUALS/vlado_13856_Magnificent_sunrise_breaking_through_clearing_sto_ae111b3c-e624-4fb0-b07d-b18319925104_1.png"
		_: return ""

func load_chapter_progress():
	"""Načítanie postupu z GameManager"""
	for i in range(chapter_data.size()):
		var chapter_num = i + 1
		chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
		chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func connect_signals():
	"""Pripojenie signálov"""
	prev_button.pressed.connect(_on_prev_button_pressed)
	next_button.pressed.connect(_on_next_button_pressed)
	start_button.pressed.connect(_on_start_button_pressed)
	back_button.pressed.connect(_on_back_button_pressed)

# === BUTTON CALLBACKS ===

func _on_prev_button_pressed():
	navigate(-1)

func _on_next_button_pressed():
	navigate(1)

func _on_start_button_pressed():
	var current_chapter = chapter_data[current_chapter_index]
	if not current_chapter.unlocked:
		show_error_animation()
		return
	
	AudioManager.play_menu_button_sound()
	animate_start_transition()
	await get_tree().create_timer(0.8).timeout
	GameManager.go_to_chapter(current_chapter.number)

func _on_back_button_pressed():
	AudioManager.play_menu_button_sound()
	animate_exit()
	await get_tree().create_timer(0.5).timeout
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func show_error_animation():
	"""Krásna animácia chyby"""
	AudioManager.play_puzzle_error_sound()
	
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Shake effect
	for i in range(3):
		tween.tween_property(chapter_card, "position:x", chapter_card.position.x + 10, 0.1)
		tween.tween_property(chapter_card, "position:x", chapter_card.position.x - 10, 0.1)
	
	# Red flash
	tween.tween_property(chapter_card, "modulate", Color(1.2, 0.8, 0.8), 0.2)
	tween.tween_property(chapter_card, "modulate", Color.WHITE, 0.3)

func animate_start_transition():
	"""Animácia pri štarte kapitoly"""
	var tween = create_tween()
	tween.set_parallel(true)
	
	tween.tween_property(chapter_card, "scale", Vector2(1.1, 1.1), 0.4)
	tween.tween_property(chapter_card, "modulate", Color(1.2, 1.1, 0.9), 0.4)
	tween.tween_property(chapter_card, "modulate:a", 0.0, 0.4)

func animate_exit():
	"""Animácia pri odchode"""
	var tween = create_tween()
	tween.set_parallel(true)
	
	tween.tween_property(chapter_card, "scale", Vector2(0.8, 0.8), 0.5)
	tween.tween_property(chapter_card, "modulate:a", 0.0, 0.5)
	tween.tween_property(chapter_card, "position:y", chapter_card.position.y + 100, 0.5)
