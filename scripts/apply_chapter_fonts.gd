extends Node

# Skript na aplikovanie gotických fontov na titulky kapitol
# Volá sa z _ready() funkcie kapitol

static func apply_chapter_title_font(label: Label):
	"""Aplikuje gotický font na titulok kapitoly s vylepšeným FontVariation systémom"""
	if not label:
		return

	# Použitie vylepšeného FontLoader systému
	FontLoader.apply_font_style(label, "chapter_title")

	print("✅ Aplikovaný chapter title font s FontVariation systémom")

static func apply_puzzle_title_font(label: Label):
	"""Aplikuje gotický font na titulky hlavolamov s vylepšeným FontVariation systémom"""
	if not label:
		return

	# Použitie vylepšeného FontLoader systému
	FontLoader.apply_font_style(label, "puzzle_text")

	print("✅ Aplikovaný puzzle title font s FontVariation systémom")
