extends Control

@onready var master_volume_slider: HSlider = $VBoxContainer/SettingsContainer/MasterVolumeContainer/MasterVolumeSlider
@onready var master_volume_label: Label = $VBoxContainer/SettingsContainer/MasterVolumeContainer/MasterVolumeLabel
@onready var music_volume_slider: HSlider = $VBoxContainer/SettingsContainer/MusicVolumeContainer/MusicVolumeSlider
@onready var music_volume_label: Label = $VBoxContainer/SettingsContainer/MusicVolumeContainer/MusicVolumeLabel
@onready var sfx_volume_slider: HSlider = $VBoxContainer/SettingsContainer/SFXVolumeContainer/SFXVolumeSlider
@onready var sfx_volume_label: Label = $VBoxContainer/SettingsContainer/SFXVolumeContainer/SFXVolumeLabel
@onready var fullscreen_button: CheckButton = $VBoxContainer/SettingsContainer/FullscreenContainer/FullscreenButton
@onready var back_button: Button = $VBoxContainer/BackButton

func _ready():
	# Nastavenie hodnôt sliderov
	master_volume_slider.min_value = 0.0
	master_volume_slider.max_value = 1.0
	master_volume_slider.step = 0.1
	master_volume_slider.value = GameManager.game_settings.master_volume
	
	music_volume_slider.min_value = 0.0
	music_volume_slider.max_value = 1.0
	music_volume_slider.step = 0.1
	music_volume_slider.value = GameManager.game_settings.music_volume
	
	sfx_volume_slider.min_value = 0.0
	sfx_volume_slider.max_value = 1.0
	sfx_volume_slider.step = 0.1
	sfx_volume_slider.value = GameManager.game_settings.sfx_volume
	
	fullscreen_button.button_pressed = GameManager.game_settings.fullscreen
	
	# Pripojenie signálov
	master_volume_slider.value_changed.connect(_on_master_volume_changed)
	music_volume_slider.value_changed.connect(_on_music_volume_changed)
	sfx_volume_slider.value_changed.connect(_on_sfx_volume_changed)
	fullscreen_button.toggled.connect(_on_fullscreen_toggled)
	back_button.pressed.connect(_on_back_pressed)
	
	# Aktualizovanie labelov
	update_volume_labels()
	
	# Nastavenie fokusu
	master_volume_slider.grab_focus()

func update_volume_labels():
	master_volume_label.text = "Hlavná hlasitosť: " + str(int(master_volume_slider.value * 100)) + "%"
	music_volume_label.text = "Hudba: " + str(int(music_volume_slider.value * 100)) + "%"
	sfx_volume_label.text = "Zvukové efekty: " + str(int(sfx_volume_slider.value * 100)) + "%"

func _on_master_volume_changed(value: float):
	GameManager.update_setting("master_volume", value)
	update_volume_labels()

func _on_music_volume_changed(value: float):
	GameManager.update_setting("music_volume", value)
	update_volume_labels()

func _on_sfx_volume_changed(value: float):
	GameManager.update_setting("sfx_volume", value)
	update_volume_labels()

func _on_fullscreen_toggled(pressed: bool):
	GameManager.update_setting("fullscreen", pressed)

func _on_back_pressed():
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
