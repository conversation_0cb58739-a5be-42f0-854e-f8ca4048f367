extends Node

# Jednoduchý test audio systému

func _ready():
	print("🎵 === AUDIO SYSTEM TEST ===")
	await get_tree().create_timer(1.0).timeout
	
	test_audio_loading()

func test_audio_loading():
	print("Testovanie načítavania audio súborov...")
	
	# Test načítania jedného MP3 súboru
	var test_path = "res://audio/music/MainTheme.mp3"
	print("Testovanie: ", test_path)
	
	var audio_stream = load(test_path)
	if audio_stream:
		print("✅ Audio súbor úspešne načítaný")
		print("Typ: ", audio_stream.get_class())
		
		if audio_stream is AudioStreamMP3:
			print("✅ Je to AudioStreamMP3")
			audio_stream.loop = true
			print("✅ Loop nastavený na true")
		else:
			print("❌ Nie je to AudioStreamMP3")
	else:
		print("❌ Nemožno načítať audio súbor")
	
	# Test AudioManager
	print("\nTestovanie AudioManager...")
	if AudioManager:
		print("✅ AudioManager existuje")
		print("Audio tracks: ", AudioManager.audio_tracks.size())
		
		# Test prehrania
		print("Testovanie prehrania main_menu...")
		AudioManager.play_music("main_menu", false)
		
		await get_tree().create_timer(3.0).timeout
		print("Test dokončený!")
	else:
		print("❌ AudioManager neexistuje")
	
	# Ukončiť po 5 sekundách
	await get_tree().create_timer(5.0).timeout
	get_tree().quit()
