extends Node

# Test script pre overenie audio systému

func _ready():
	print("🎵 === AUDIO SYSTEM TEST ===")
	test_audio_tracks()
	test_puzzle_system()
	test_chapter_music_mapping()
	print("🎵 === TEST DOKONČENÝ ===")
	
	# Ukončiť po 3 sekundách
	await get_tree().create_timer(3.0).timeout
	get_tree().quit()

func test_audio_tracks():
	print("\n📁 Testovanie audio trackov...")
	
	var audio_tracks = {
		"main_menu": "res://audio/music/MainTheme.mp3",
		"storm_journey": "res://audio/music/2. Storm Journey - Búrlivá cesta.mp3",
		"forest_shadows": "res://audio/music/3. Forest of Shadows - Temný les.mp3",
		"castle_gates": "res://audio/music/4. Castle Gates - Brána hrôzy.mp3",
		"viktor_theme": "res://audio/music/5. <PERSON>'s Theme - Vern<PERSON> služobník.mp3",
		"library_secrets": "res://audio/music/6. Library of Secrets - Tajomná knižnica.mp3",
		"puzzle_theme": "res://audio/music/7. Puzzle Theme - Hádanky.mp3",
		"alchemy_lab": "res://audio/music/10. Alchemy Laboratory - Alchýmia.mp3",
		"descent_darkness": "res://audio/music/11. Descent into Darkness - Zostup do katakomb.mp3",
		"ancient_crypts": "res://audio/music/12. Ancient Crypts - Pradávne hrobky.mp3",
		"isabelle_awakening": "res://audio/music/13. Isabelle's Awakening - Prebudenie zla.mp3",
		"final_ritual": "res://audio/music/14. Final Ritual - Posledný rituál.mp3",
		"van_helsing_rescue": "res://audio/music/15. Van Helsing 15. Rescued - Záchrana mentora.mp3"
	}
	
	for track_name in audio_tracks:
		var file_path = audio_tracks[track_name]
		if ResourceLoader.exists(file_path):
			print("✅ ", track_name, " - súbor existuje")
		else:
			print("❌ ", track_name, " - súbor NEEXISTUJE: ", file_path)

func test_puzzle_system():
	print("\n🧩 Testovanie puzzle systému...")
	
	# Simulácia puzzle systému
	print("🎵 Spúšťam puzzle hudbu...")
	AudioManager.start_puzzle()
	
	await get_tree().create_timer(1.0).timeout
	
	print("🔙 Návrat z puzzle...")
	AudioManager.return_from_puzzle()

func test_chapter_music_mapping():
	print("\n📖 Testovanie mapovanie kapitol...")
	
	var scene_music_map = {
		1: "storm_journey",
		2: "castle_gates",
		3: "library_secrets",
		4: "alchemy_lab",
		5: "descent_darkness",
		6: "isabelle_awakening",
		7: "van_helsing_rescue"
	}
	
	for chapter in scene_music_map:
		var music = scene_music_map[chapter]
		print("📖 Kapitola ", chapter, " → ", music)
	
	print("\n🎭 Testovanie špeciálnych funkcií...")
	print("🤵 Viktor theme test...")
	AudioManager.meet_viktor()
	
	await get_tree().create_timer(0.5).timeout
	
	print("⚔️ Final battle test...")
	AudioManager.final_battle()
	
	await get_tree().create_timer(0.5).timeout
	
	print("🏛️ Ancient crypts test...")
	AudioManager.enter_crypts()
