@tool
extends EditorScript

# Skript na aplikovanie gotických fontov na všetky scény
# Spustí sa v editore pre hromadnú aktualizáciu

func _run():
	print("=== APLIKOVANIE GOTICKÝCH FONTOV ===")
	
	# Zoznam scén na aktualizáciu
	var scenes_to_update = [
		"res://scenes/Chapter2.tscn",
		"res://scenes/Chapter3.tscn", 
		"res://scenes/Chapter4.tscn",
		"res://scenes/Chapter5.tscn",
		"res://scenes/Chapter6.tscn",
		"res://scenes/Chapter7.tscn",
		"res://scenes/MainMenu.tscn",
		"res://scenes/ChaptersMenu.tscn"
	]
	
	for scene_path in scenes_to_update:
		update_scene_fonts(scene_path)
	
	print("=== DOKONČENÉ ===")

func update_scene_fonts(scene_path: String):
	print("Aktualizujem: ", scene_path)
	
	# Nač<PERSON>tanie scény
	var scene = load(scene_path)
	if not scene:
		print("CHYBA: Nemožno načítať scénu: ", scene_path)
		return
	
	var scene_instance = scene.instantiate()
	if not scene_instance:
		print("CHYBA: Nemožno vytvoriť inštanciu scény: ", scene_path)
		return
	
	# Nájdenie a aktualizácia ChapterTitle
	var chapter_title = find_node_by_name(scene_instance, "ChapterTitle")
	if chapter_title and chapter_title is Label:
		apply_chapter_title_font(chapter_title)
		print("  - Aktualizovaný ChapterTitle")
	
	# Nájdenie a aktualizácia TitleLabel (pre menu)
	var title_label = find_node_by_name(scene_instance, "TitleLabel")
	if title_label and title_label is Label:
		apply_chapter_title_font(title_label)
		print("  - Aktualizovaný TitleLabel")
	
	# Uloženie scény
	var packed_scene = PackedScene.new()
	packed_scene.pack(scene_instance)
	ResourceSaver.save(packed_scene, scene_path)
	
	scene_instance.queue_free()
	print("  - Scéna uložená")

func find_node_by_name(node: Node, name: String) -> Node:
	"""Rekurzívne hľadá node podľa mena"""
	if node.name == name:
		return node
	
	for child in node.get_children():
		var result = find_node_by_name(child, name)
		if result:
			return result
	
	return null

func apply_chapter_title_font(label: Label):
	"""Aplikuje gotický font na titulok kapitoly"""
	# Načítanie font settings
	var title_settings = load("res://themes/ChapterTitleSettings.tres")
	if title_settings:
		# Aplikovanie LabelSettings
		label.label_settings = title_settings
		print("    - Aplikovaný gotický font")
	else:
		print("    - CHYBA: Nemožno načítať ChapterTitleSettings.tres")
