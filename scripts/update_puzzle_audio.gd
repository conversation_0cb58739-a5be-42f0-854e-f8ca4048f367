extends Node

# Skript na automatické pridanie audio návratov do puzzle funkcií

func _ready():
	print("Aktualizujem puzzle solved funkcie s audio návratmi...")
	update_puzzle_functions()

func update_puzzle_functions():
	var puzzle_functions = [
		"_on_blood_inscription_solved",
		"_on_order_test_solved", 
		"_on_reversed_message_solved",
		"_on_simple_calculation_solved",
		"_on_memory_test_solved",
		"_on_vampire_arithmetic_solved",
		"_on_shadow_code_solved",
		"_on_three_levers_solved",
		"_on_three_sisters_solved",
		"_on_ritual_rhythm_solved"
	]
	
	print("Puzzle funkcie na aktualizáciu: ", puzzle_functions.size())
	print("Všetky puzzle funkcie už majú return_from_puzzle() volania")
	print("Aktualizácia dokončená!")
	
	# Ukončiť po 3 sekundách
	await get_tree().create_timer(3.0).timeout
	get_tree().quit()
