# Dynamické pozadia pre Kapitolu 5: Krypty

## Prehľad implementácie

Kapitola 5 teraz používa dynamické pozadia z priečinka `assets/pozadia/Kapitola_5/` podľa logiky príbehu a dialógov.

## Logika pozadí

### **Story Phase 0 (Úvod)**
- **Pozadie**: `1.png` 
- **Kontext**: "Za laboratóriom sa skrýva úzke kamenné schodisko vedúce do hlbín"
- **Dialógy**: Úvodné dialógy s Viktorom o zostupe do krypt, požehnaný kríž od arcibiskupa

### **Story Phase 1 (Po úvodných dialógoch)**
- **Pozadie**: `2.png`
- **Kontext**: Pred prvou hádankou (Kód z tieňov)
- **Akcia**: Zobrazenie prvého hlavolamu so sviečkami

### **Story Phase 2 (Po prvom puzzle)**
- **Pozadie**: `3.png` (zmena pri vete "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi")
- **Kontext**: Interlude dialógy, vstup do oválnej sály s Van Helsingovými vecami
- **Dialógy**: Van Helsingove zápisky → "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi"

### **Story Phase 3 (Po interlude dialógoch)**
- **Pozadie**: `3.png` (zostáva v oválnej sále)
- **Kontext**: Pokračovanie v oválnej sále s mohutným sarkofágom
- **Akcia**: Zobrazenie druhého hlavolamu (Tri páky)

### **Počas puzzle**
- **Pozadie**: `UI_Pozadie.png` (štandardné pozadie pre všetky hlavolamy)

## Implementované zmeny

### **1. scenes/Chapter5.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_5/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_5 (kamenné schodisko)

### **2. scripts/Chapter.gd**

**Zmena pozadia po úvodných dialógoch:**
```gdscript
# Po úvodných dialógoch - ukázať prvý hlavolam
story_phase = 1
# Pre Kapitolu 5 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
elif chapter_number == 5:
    change_background_image("res://assets/pozadia/Kapitola_5/2.png")
```

**Návrat pozadia po prvom puzzle:**
```gdscript
func _on_shadow_code_solved():
    # Pre kapitolu 5 - pokračovať v príbehu
    if chapter_number == 5:
        story_phase = 2
        hide_puzzle_buttons()
        # Vrátiť pozadie na 2.png (pozadie pred prvou hádankou)
        change_background_image("res://assets/pozadia/Kapitola_5/2.png")
        # Spustiť interlude dialógy (pozadie sa zmení automaticky pri konkrétnej vete)
        if dialogue_system:
            var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
            dialogue_system.start_dialogue(interlude_dialogue)
```

**Návrat pozadia po druhom puzzle:**
```gdscript
func _on_three_levers_solved():
    # Pre kapitolu 5 - dokončiť kapitolu a spustiť ancient crypts hudbu
    if chapter_number == 5:
        # Vrátiť pozadie na 3.png (pozadie pred druhou hádankou)
        change_background_image("res://assets/pozadia/Kapitola_5/3.png")
        # Spustiť ancient crypts hudbu pre vstup do krypt
        AudioManager.enter_crypts()
        complete_puzzle(2)
```

### **3. scripts/DialogueSystem.gd**

**Zmena pozadia počas interlude dialógov:**
```gdscript
# V DialogueSystem.gd - detekcia konkrétnej vety
elif text == "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_5/3.png")

# V Chapter.gd - spracovanie signálu
func _on_background_change_requested(image_path: String):
    change_background_image(image_path)
```

**Pozadie počas puzzle:**
```gdscript
func show_puzzle_scene(puzzle_number: int):
    # Pri spustení puzzle vrátiť pozadie na UI_Pozadie.png
    change_background_image("res://assets/Obrázky/UI_Pozadie.png")
```

## Súhrn zmien pozadí

| Fáza príbehu | Pozadie | Popis |
|--------------|---------|-------|
| Story Phase 0 | `1.png` | Kamenné schodisko do hlbín |
| Story Phase 1 | `2.png` | Po úvodných dialógoch (pred prvou hádankou) |
| Story Phase 2 | `3.png` | Pri vete "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi" |
| Story Phase 3 | `3.png` | Zostáva v oválnej sále |
| Počas puzzle | `UI_Pozadie.png` | Štandardné pozadie |

## Automatický prechod do kapitoly 6

Kapitola 5 má implementované záverečné dialógy, ktoré sa spustia po dokončení druhého hlavolamu:
- Po vyriešení "Tri páky" sa spustia záverečné dialógy o sarkofágu
- Po dokončení záverečných dialógov sa zobrazí dialóg dokončenia kapitoly
- Po stlačení tlačidla "Ďalej" sa okamžite načíta Kapitola 6 (plynulý prechod)

## Testovanie

Pre testovanie dynamických pozadí:
1. Spustite Kapitolu 5
2. Prejdite cez úvodné dialógy - pozadie sa zmení na 2.png (pred prvou hádankou)
3. Vyriešte prvý hlavolam (Kód z tieňov) - spustia sa interlude dialógy
4. Sledujte vetu "Schádzate do oválnej sály s klenbou pokrytou prastarými symbolmi" - pozadie sa zmení na oválnu sálu (3.png)
5. Dokončite interlude dialógy - pozadie zostane na 3.png
6. Vyriešte druhý hlavolam (Tri páky) - spustia sa záverečné dialógy
7. Dokončite záverečné dialógy - automatický prechod na Kapitolu 6

## Poznámky

- Implementácia sleduje rovnakú logiku ako Kapitoly 1, 2, 3 a 4
- Pozadia sa menia podľa naratívneho toku príbehu a konkrétnych viet v dialógoch
- Puzzle používajú štandardné pozadie `UI_Pozadie.png`
- Zmeny sú konzistentné s existujúcim systémom dynamických pozadí
- Zmena pozadia na oválnu sálu sa spúšťa presne pri vete o oválnej sále
- Po vyriešení puzzle sa pozadie vráti na správny obrázok pred hádankou
