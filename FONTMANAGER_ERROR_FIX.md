# 🔧 FONTMANAGER ERROR FIX

## ❌ **Chyba:**
```
<PERSON><PERSON><PERSON> Error: Identifier "FontManager" not declared in the current scope.
```

## 🔍 **Príčina:**
- V MainMenu.gd som použil nesprávny názov `FontManager`
- Správny názov triedy je `FontLoader`

## ✅ **Oprava:**

### **PREDTÝM (nesprávne):**
```gdscript
# Aplikuj font
if FontManager:
    FontManager.apply_ui_font(button)
```

### **TERAZ (správne):**
```gdscript
# Aplikuj font
FontLoader.apply_ui_font(button)
```

## 📚 **Správny FontLoader systém:**

### **Dostupné funkcie:**
```gdscript
# Aplikovanie štýlov
FontLoader.apply_font_style(label, "ui_elements")
FontLoader.apply_font_style(label, "chapter_title")
FontLoader.apply_font_style(label, "character_dialogue")
FontLoader.apply_font_style(label, "narrator_text")
FontLoader.apply_font_style(label, "puzzle_text")

# Utility funkcie
FontLoader.apply_ui_font(button)
FontLoader.apply_chapter_title_font(label)
FontLoader.apply_character_dialogue_font(label)
FontLoader.apply_narrator_font(label)
FontLoader.apply_puzzle_font(label)
```

### **Typy fontov v systéme:**
- **"ui_elements"** → Montserrat (biela, medium)
- **"chapter_title"** → Cinzel (zlatá, bold)
- **"character_dialogue"** → Cormorant Garamond (krémová)
- **"narrator_text"** → Crimson Text (sivá, italic)
- **"puzzle_text"** → Uncial Antiqua (hnedá)

## 🎨 **Aktualizovaný MainMenu button styling:**

```gdscript
func setup_button_style(button: Button, ramcek_texture: Texture2D):
    # Nastav rámček ako pozadie buttonu
    button.texture_normal = ramcek_texture
    button.texture_hover = ramcek_texture
    button.texture_pressed = ramcek_texture
    
    # Nastav veľkosť buttonu
    button.custom_minimum_size = Vector2(300, 60)
    
    # Aplikuj font - OPRAVENÉ
    FontLoader.apply_ui_font(button)
    button.add_theme_font_size_override("font_size", 20)
    
    # Centruj text
    button.alignment = HORIZONTAL_ALIGNMENT_CENTER
    
    # Hover effect
    button.mouse_entered.connect(func(): button.modulate = Color(1.2, 1.2, 1.2))
    button.mouse_exited.connect(func(): button.modulate = Color.WHITE)
```

## 🎯 **Výsledok:**

### **Funkčné vlastnosti:**
- ✅ **Žiadne parser chyby** - FontLoader je správne deklarovaný
- ✅ **Gothic UI font** - Montserrat medium, biela farba
- ✅ **Responzívne škálovanie** - automatické podľa screen size
- ✅ **RAMCEK.png rámčeky** - na všetkých tlačidlách
- ✅ **Hover effects** - 1.2x modulate

### **Font vlastnosti pre UI elementy:**
- **Font:** Montserrat (fallback: Arial, Helvetica, sans-serif)
- **Farba:** Biela (#FFFFFF)
- **Veľkosť:** 20px (custom override)
- **Štýl:** Medium weight
- **Efekty:** Žiadne shadow/outline (čistý vzhľad)

**MainMenu teraz funguje bez chýb s gothic fontami!** ✅🎮
