# Dynamické pozadia pre Kapitoly 6 a 7

## Kapitola 6: Konfrontácia

### Prehľad implement<PERSON><PERSON> 6 teraz používa dynamické pozadia z priečinka `assets/pozadia/Kapitola_6/` podľa logiky príbehu a dialógov.

### Logika pozadí

#### **Story Phase 0 (Úvod)**
- **Pozadie**: `1.png` 
- **Kontext**: "Keď sa posledná pečať uvoľní, celý sarkofág sa otrasie"
- **Dialógy**: Úvodné dialógy s Isabelle, otvorenie sarkofágu

#### **Story Phase 1 (Po úvodných dialógoch)**
- **Pozadie**: `2.png`
- **Kontext**: Pred prvou hádankou (Tri sestry)
- **Akcia**: Zobrazenie prvého hlavolamu s portrétmi

#### **Story Phase 2 (Po prvom puzzle)**
- **Pozadie**: `3.png` (zmena pri vete "<PERSON> vystúpi zo sarkofágu")
- **Kontext**: Interlude dialógy, finálny sú<PERSON>j s <PERSON>
- **Dialógy**: "Bystré! No múdrosť vás neochráni pred mojou silou" → "Isabelle vystúpi zo sarkofágu"

#### **Story Phase 3 (Po interlude dialógoch)**
- **Pozadie**: `3.png` (zostáva v finálnom súboji)
- **Kontext**: Pokračovanie finálneho súboja
- **Akcia**: Zobrazenie druhého hlavolamu (Rytmus rituálu)

### Implementované zmeny pre Kapitolu 6

#### **1. scenes/Chapter6.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_6/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_6 (otvorenie sarkofágu)

#### **2. scripts/Chapter.gd**
```gdscript
# Zmena pozadia po úvodných dialógoch
elif chapter_number == 6:
    change_background_image("res://assets/pozadia/Kapitola_6/2.png")

# Návrat pozadia po prvom puzzle
if chapter_number == 6:
    change_background_image("res://assets/pozadia/Kapitola_6/2.png")

# Návrat pozadia po druhom puzzle
if chapter_number == 6:
    change_background_image("res://assets/pozadia/Kapitola_6/3.png")
```

#### **3. scripts/DialogueSystem.gd**
```gdscript
elif text == "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_6/3.png")
```

---

## Kapitola 7: Epilóg

### Prehľad implementácie
Kapitola 7 (Epilóg) používa dynamické pozadia z priečinka `assets/pozadia/Kapitola_7/` s špeciálnou logikou pre záverečné dialógy.

### Logika pozadí

#### **Story Phase 0 (Úvod)**
- **Pozadie**: `1.png` 
- **Kontext**: "Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie"
- **Dialógy**: Úvodné dialógy epilógu, záchrana Van Helsinga

#### **Story Phase 1 (Záverečné dialógy)**
- **Pozadie**: `2.png`
- **Kontext**: Záverečné dialógy, návrat a koniec hry
- **Dialógy**: Dialógy s Viktorom, odchod zo zámku, záver príbehu

### Implementované zmeny pre Kapitolu 7

#### **1. scenes/Chapter7.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_7/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_7 (skrytá komora)

#### **2. scripts/Chapter.gd**
```gdscript
if chapter_number == 7:
    # Pre epilóg - zmeniť pozadie a pokračovať v dialógoch
    if story_phase == 0:
        story_phase = 1
        # Zmeniť pozadie na druhý obrázok (záverečné dialógy)
        change_background_image("res://assets/pozadia/Kapitola_7/2.png")
        # Spustiť záverečné dialógy epilógu
        if dialogue_system:
            var final_dialogue = dialogue_system.get_final_dialogue(chapter_number)
            dialogue_system.start_dialogue(final_dialogue)
    else:
        # Po všetkých dialógoch ukončiť hru
        GameManager.complete_epilogue()
        await get_tree().create_timer(3.0).timeout
        GameManager.go_to_main_menu()
```

#### **3. scripts/DialogueSystem.gd**
```gdscript
func get_final_dialogue(chapter_number: int) -> Array[Dictionary]:
    if chapter_number == 7:
        return [
            {"speaker": "Rozprávač", "text": "Stúpate späť chodbami zámku. Viktor vás čaká s úľavou v očiach."},
            {"speaker": "Viktor", "text": "Pán doktor! Vďaka Bohu, že žijete!"},
            # ... záverečné dialógy
        ]
```

## Súhrn zmien pozadí

### Kapitola 6
| Fáza príbehu | Pozadie | Popis |
|--------------|---------|-------|
| Story Phase 0 | `1.png` | Otvorenie sarkofágu |
| Story Phase 1 | `2.png` | Po úvodných dialógoch (pred prvou hádankou) |
| Story Phase 2 | `3.png` | Pri vete "Isabelle vystúpi zo sarkofágu" |
| Story Phase 3 | `3.png` | Zostáva v finálnom súboji |
| Počas puzzle | `UI_Pozadie.png` | Štandardné pozadie |

### Kapitola 7 (Epilóg)
| Fáza príbehu | Pozadie | Popis |
|--------------|---------|-------|
| Story Phase 0 | `1.png` | Skrytá komora s Van Helsingom |
| Story Phase 1 | `2.png` | Záverečné dialógy, návrat |

## Automatický prechod

- **Kapitola 6** → Po dokončení druhého puzzle → Záverečné dialógy → Automatický prechod na Epilóg (Kapitola 7)
- **Kapitola 7** → Po dokončení všetkých dialógov → Návrat do hlavného menu

## Testovanie

### Kapitola 6:
1. Spustite Kapitolu 6 - pozadie: otvorenie sarkofágu (1.png)
2. Prejdite úvodné dialógy - pozadie sa zmení na 2.png
3. Vyriešte Tri sestry - pozadie sa vráti na 2.png, spustia sa interlude dialógy
4. Sledujte vetu "Isabelle vystúpi zo sarkofágu" - pozadie sa zmení na finálny súboj (3.png)
5. Vyriešte Rytmus rituálu - pozadie sa vráti na 3.png, spustia sa záverečné dialógy
6. Dokončite záverečné dialógy - automatický prechod na Epilóg

### Kapitola 7:
1. Automaticky sa načíta po Kapitole 6 - pozadie: skrytá komora (1.png)
2. Prejdite úvodné dialógy - pozadie sa zmení na 2.png
3. Dokončite záverečné dialógy - návrat do hlavného menu

## Poznámky

- Implementácia sleduje rovnakú logiku ako kapitoly 1-5
- Kapitola 6 má štandardné pozadia s návratom po puzzle
- Kapitola 7 má špeciálnu logiku pre epilóg bez hlavolamov
- Pozadia sa menia podľa naratívneho toku príbehu
- Automatický prechod medzi kapitolami funguje správne
