# Implementácia dynamických pozadí pre Kapitoly 6 a 7

## ✅ Úspešne implementované

### **Kapitola 6: Konfrontácia**

#### **Zmeny v súboroch:**
- **scenes/Chapter6.tscn** - počiatočné pozadie zmenené na `1.png` (otvorenie sarkofágu)
- **scripts/Chapter.gd** - pridaná logika pre zmeny pozadia a návrat po puzzle
- **scripts/DialogueSystem.gd** - detekcia vety "Isabelle vystúpi zo sarkofágu"

#### **Logika pozadí:**
| Fáza | Pozadie | Trigger | Popis |
|------|---------|---------|-------|
| **Úvod** | `1.png` | Počiatočné pozadie | Otvorenie sarkofágu |
| **Po úvodných dialógoch** | `2.png` | Po dokončení úvodných dialógov | Pred prvou hádankou (Tri sestry) |
| **Po prvom puzzle** | `2.png` | Návrat po vyriešení puzzle | Pozadie pred prvou hádankou |
| **Interlude dialógy** | `3.png` | Pri vete o Isabelle | Finálny súboj s Isabelle |
| **Po druhom puzzle** | `3.png` | Návrat po vyriešení puzzle | Pozadie pred druhou hádankou |
| **Počas puzzle** | `UI_Pozadie.png` | Pri spustení puzzle | Štandardné pozadie |

#### **Naratívna logika:**
1. **1.png**: "Keď sa posledná pečať uvoľní, celý sarkofág sa otrasie"
2. **2.png**: Po úvodných dialógoch s Isabelle
3. **3.png**: "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník."

---

### **Kapitola 7: Epilóg**

#### **Zmeny v súboroch:**
- **scenes/Chapter7.tscn** - počiatočné pozadie zmenené na `1.png` (skrytá komora)
- **scripts/Chapter.gd** - špeciálna logika pre epilóg bez hlavolamov
- **scripts/DialogueSystem.gd** - pridaná funkcia `get_final_dialogue()`, rozdelené dialógy

#### **Logika pozadí:**
| Fáza | Pozadie | Trigger | Popis |
|------|---------|---------|-------|
| **Úvod** | `1.png` | Počiatočné pozadie | Skrytá komora s Van Helsingom |
| **Záverečné dialógy** | `2.png` | Po úvodných dialógoch | Návrat, záver príbehu |

#### **Naratívna logika:**
1. **1.png**: "Keď Isabelle zmizne, jedna zo stien sa pomaly odsunie"
2. **2.png**: Záverečné dialógy s Viktorom, odchod zo zámku

#### **Špeciálna implementácia pre epilóg:**
```gdscript
if chapter_number == 7:
    if story_phase == 0:
        story_phase = 1
        change_background_image("res://assets/pozadia/Kapitola_7/2.png")
        # Spustiť záverečné dialógy
        var final_dialogue = dialogue_system.get_final_dialogue(chapter_number)
        dialogue_system.start_dialogue(final_dialogue)
    else:
        # Po všetkých dialógoch ukončiť hru
        GameManager.complete_epilogue()
        GameManager.go_to_main_menu()
```

## 🎯 **Testovací scenár**

### **Kapitola 6:**
1. **Spustite Kapitolu 6** - pozadie: otvorenie sarkofágu (1.png)
2. **Prejdite úvodné dialógy** - pozadie sa zmení na 2.png
3. **Vyriešte Tri sestry** - pozadie sa vráti na 2.png, spustia sa interlude dialógy
4. **Sledujte vetu o Isabelle** - pozadie sa zmení na finálny súboj (3.png)
5. **Vyriešte Rytmus rituálu** - pozadie sa vráti na 3.png, spustia sa záverečné dialógy
6. **Dokončite záverečné dialógy** - automatický prechod na Epilóg

### **Kapitola 7:**
1. **Automaticky sa načíta** - pozadie: skrytá komora (1.png)
2. **Prejdite úvodné dialógy** - pozadie sa zmení na 2.png
3. **Dokončite záverečné dialógy** - návrat do hlavného menu

## 📋 **Kontrolný zoznam**

### **Kapitola 6:**
- ✅ Počiatočné pozadie zmenené na 1.png
- ✅ Zmena pozadia po úvodných dialógoch na 2.png
- ✅ Návrat pozadia po prvom puzzle na 2.png
- ✅ Detekcia vety o Isabelle implementovaná
- ✅ Automatická zmena pozadia na 3.png pri konkrétnej vete
- ✅ Návrat pozadia po druhom puzzle na 3.png
- ✅ Puzzle používajú štandardné pozadie UI_Pozadie.png
- ✅ Automatický prechod do epilógu funguje

### **Kapitola 7:**
- ✅ Počiatočné pozadie zmenené na 1.png
- ✅ Zmena pozadia po úvodných dialógoch na 2.png
- ✅ Špeciálna logika pre epilóg bez hlavolamov
- ✅ Rozdelené dialógy na dve časti
- ✅ Funkcia get_final_dialogue() implementovaná
- ✅ Návrat do hlavného menu po dokončení

### **Všeobecné:**
- ✅ Žiadne chyby v diagnostike
- ✅ Všetky potrebné obrázky sú dostupné
- ✅ Konzistencia so systémom kapitol 1-5

## 🎨 **Dostupné pozadia**

### **Kapitola 6:**
- `assets/pozadia/Kapitola_6/1.png` - Otvorenie sarkofágu
- `assets/pozadia/Kapitola_6/2.png` - Pred prvou hádankou
- `assets/pozadia/Kapitola_6/3.png` - Finálny súboj s Isabelle

### **Kapitola 7:**
- `assets/pozadia/Kapitola_7/1.png` - Skrytá komora s Van Helsingom
- `assets/pozadia/Kapitola_7/2.png` - Záverečné dialógy, návrat

## 🔗 **Konzistencia so systémom**

Implementácia sleduje rovnakú logiku ako kapitoly 1-5:
- Počiatočné pozadie z assets/pozadia/Kapitola_X/1.png
- Zmena pozadia po úvodných dialógoch
- Detekcia konkrétnych viet pre zmenu pozadia (Kapitola 6)
- Návrat pozadia po vyriešení puzzle (Kapitola 6)
- Štandardné pozadie pre puzzle (Kapitola 6)
- Automatický prechod medzi kapitolami

## 🏆 **Špecifické pre finálne kapitoly**

### **Kapitola 6:**
- **Téma**: Finálny súboj s grófkou Isabelle Báthoryovou
- **Hlavolamy**: Tri sestry (logická hádanka) a Rytmus rituálu (sekvencia symbolov)
- **Kľúčová veta**: "Isabelle vystúpi zo sarkofágu. Už nie je tou kráskou z kroník."
- **Hudba**: Špeciálna final battle hudba po prvom puzzle

### **Kapitola 7:**
- **Téma**: Záchrana Van Helsinga a záver príbehu
- **Žiadne hlavolamy**: Čisto naratívny epilóg
- **Rozdelené dialógy**: Úvodné (záchrana) + záverečné (návrat)
- **Koniec hry**: Automatický návrat do hlavného menu

**Kapitoly 6 a 7 sú pripravené s dynamickými pozadiami!** 🎮🏰✨
