# Implementácia dynamických pozadí pre Kapitolu 4: <PERSON><PERSON><PERSON>

## ✅ Úspešne implementované

### **Zmeny v súboroch:**

#### **1. scenes/Chapter4.tscn**
- Zmenené počiatočné pozadie z `UI_Pozadie.png` na `1.png` (star<PERSON> krídlo s mechanizmami)

#### **2. scripts/Chapter.gd**
- Pridaná logika pre zmenu pozadia po úvodných dialógoch na `2.png`

#### **3. scripts/DialogueSystem.gd**
- Pridaná detekcia vety "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami"
- Automatická zmena pozadia na `3.png` pri tejto vete

### **Logika pozadí podľa príbehu:**

| Fáza | Pozadie | Trigger | Popis |
|------|---------|---------|-------|
| **Úvod** | `3.png` | Počiatočné pozadie | Staré krídlo s mechanizmami |
| **Po úvodných dialógoch** | `2.png` | Po dokončení úvodných dialógov | Pred prvou hádankou |
| **Interlude dialógy** | `1.png` | Pri vete o laboratóriu | Laboratórium s alchymistickými prístrojmi |
| **Po interlude dialógoch** | `1.png` | Zostáva v laboratóriu | Pokračovanie v laboratóriu |
| **Počas puzzle** | `UI_Pozadie.png` | Pri spustení puzzle | Štandardné pozadie |

### **Naratívna logika:**

1. **3.png**: "Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší."
2. **2.png**: Po úvodných dialógoch, pred pamäťovým testom
3. **1.png**: "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami."

### **Implementované funkcie:**

```gdscript
# V Chapter.gd - zmena pozadia po úvodných dialógoch
elif chapter_number == 4:
    change_background_image("res://assets/pozadia/Kapitola_4/2.png")

# V DialogueSystem.gd - detekcia konkrétnej vety
elif text == "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_4/1.png")
```

### **Automatický prechod kapitol:**

1. **Dokončenie druhého puzzle** → Záverečné dialógy o ochrannom elixíre
2. **Dokončenie záverečných dialógov** → Dialóg dokončenia kapitoly (3s)
3. **Automatický prechod** → Kapitola 5

## 🎯 **Testovací scenár**

1. **Spustite Kapitolu 4** - pozadie: staré krídlo (1.png)
2. **Prejdite úvodné dialógy** - pozadie sa zmení na 2.png
3. **Vyriešte Pamäťový test** - spustia sa interlude dialógy
4. **Sledujte vetu o laboratóriu** - pozadie sa zmení na laboratórium (3.png)
5. **Dokončite interlude dialógy** - pozadie zostane na 3.png
6. **Vyriešte Vampírsku aritmetiku** - spustia sa záverečné dialógy
7. **Dokončite záverečné dialógy** - automatický prechod na Kapitolu 5

## 📋 **Kontrolný zoznam**

- ✅ Počiatočné pozadie zmenené na 3.png
- ✅ Zmena pozadia po úvodných dialógoch na 2.png
- ✅ Detekcia vety o laboratóriu implementovaná
- ✅ Automatická zmena pozadia na 1.png pri konkrétnej vete
- ✅ Pozadie zostáva na 1.png po interlude dialógoch
- ✅ Puzzle používajú štandardné pozadie UI_Pozadie.png
- ✅ Automatický prechod do kapitoly 5 funguje
- ✅ Žiadne chyby v diagnostike
- ✅ Všetky potrebné obrázky sú dostupné

## 🎨 **Dostupné pozadia**

- `assets/pozadia/Kapitola_4/1.png` - Alchymistické laboratórium
- `assets/pozadia/Kapitola_4/2.png` - Chodba pred pamäťovým testom
- `assets/pozadia/Kapitola_4/3.png` - Staré krídlo s mechanizmami

## 🔗 **Konzistencia so systémom**

Implementácia sleduje rovnakú logiku ako kapitoly 1, 2 a 3:
- Počiatočné pozadie z assets/pozadia/Kapitola_X/1.png
- Zmena pozadia po úvodných dialógoch
- Detekcia konkrétnych viet pre zmenu pozadia
- Štandardné pozadie pre puzzle
- Automatický prechod medzi kapitolami

**Kapitola 4 je pripravená s dynamickými pozadiami!** 🎮✨
